-- =====================================================
-- jshERP 库存盘点模块验证脚本
-- 用于验证菜单配置和权限分配是否正确
-- =====================================================

-- =====================================================
-- 执行前检查脚本
-- =====================================================

-- 1. 检查数据库连接和基本信息
SELECT 
    DATABASE() as current_database,
    USER() as current_user,
    NOW() as current_time;

-- 2. 检查jsh_function表结构
DESCRIBE jsh_function;

-- 3. 查看现有菜单结构
SELECT 
    number,
    name,
    parent_number,
    url,
    component,
    sort,
    enabled,
    icon
FROM jsh_function 
WHERE delete_flag = '0' 
ORDER BY 
    CASE WHEN parent_number = '0' THEN number ELSE parent_number END,
    parent_number,
    sort;

-- 4. 检查编号09是否已被使用
SELECT 
    number,
    name,
    parent_number,
    url,
    'CONFLICT: 编号09已被使用' as status
FROM jsh_function 
WHERE number = '09' AND delete_flag = '0'
UNION ALL
SELECT 
    '09' as number,
    '可用' as name,
    '' as parent_number,
    '' as url,
    'OK: 编号09可以使用' as status
WHERE NOT EXISTS (
    SELECT 1 FROM jsh_function 
    WHERE number = '09' AND delete_flag = '0'
);

-- 5. 检查相关编号是否已被使用
SELECT 
    number,
    name,
    CASE 
        WHEN number IN ('09', '0901', '0902') THEN 'CONFLICT: 编号已被使用'
        ELSE 'INFO: 其他编号'
    END as status
FROM jsh_function 
WHERE number IN ('09', '0901', '0902', '0903', '0904', '0905') 
  AND delete_flag = '0'
ORDER BY number;

-- =====================================================
-- 角色和权限检查脚本
-- =====================================================

-- 6. 查看所有角色
SELECT 
    id,
    name,
    value,
    description,
    enabled
FROM jsh_role 
WHERE delete_flag = '0'
ORDER BY id;

-- 7. 查看用户角色关系
SELECT 
    ub.id,
    ub.type,
    ub.key_id,
    r.name as role_name,
    LENGTH(ub.value) as permission_length,
    SUBSTRING(ub.value, 1, 100) as permission_preview
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.delete_flag = '0'
ORDER BY ub.key_id;

-- 8. 检查当前用户的权限
SELECT 
    u.username,
    u.login_name,
    ub.key_id as role_id,
    r.name as role_name,
    ub.value as permissions
FROM jsh_user u
JOIN jsh_user_business ub_user ON u.id = ub_user.value
JOIN jsh_user_business ub ON ub_user.key_id = ub.key_id
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub_user.type = 'UserRole'
  AND ub.type = 'RoleFunctions'
  AND u.delete_flag = '0'
  AND ub.delete_flag = '0'
ORDER BY u.username, r.name;

-- =====================================================
-- 配置执行后验证脚本
-- =====================================================

-- 9. 验证菜单创建是否成功
SELECT 
    '菜单创建验证' as check_type,
    number,
    name,
    parent_number,
    url,
    component,
    enabled,
    CASE 
        WHEN number = '09' AND name = '盘点业务' AND parent_number = '0' THEN 'OK'
        WHEN number = '0901' AND name = '盘点复盘' AND parent_number = '09' THEN 'OK'
        WHEN number = '0902' AND name = '盘点录入' AND parent_number = '09' THEN 'OK'
        ELSE 'ERROR: 配置不正确'
    END as status
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0'
ORDER BY number;

-- 10. 验证菜单层级关系
SELECT 
    '菜单层级验证' as check_type,
    p.number as parent_number,
    p.name as parent_name,
    c.number as child_number,
    c.name as child_name,
    CASE 
        WHEN p.number = '09' AND c.parent_number = '09' THEN 'OK'
        ELSE 'ERROR: 层级关系错误'
    END as status
FROM jsh_function p
LEFT JOIN jsh_function c ON p.number = c.parent_number
WHERE p.number = '09' 
  AND p.delete_flag = '0'
  AND (c.delete_flag = '0' OR c.delete_flag IS NULL)
ORDER BY c.number;

-- 11. 验证组件路径配置
SELECT 
    '组件路径验证' as check_type,
    number,
    name,
    component,
    CASE 
        WHEN number = '09' AND component = 'layouts/RouteView' THEN 'OK'
        WHEN number = '0901' AND component = '/inventory/InventoryCheckList' THEN 'OK'
        WHEN number = '0902' AND component = '/inventory/InventoryInputList' THEN 'OK'
        ELSE 'WARNING: 组件路径可能不正确'
    END as status
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0'
ORDER BY number;

-- 12. 验证权限分配是否成功
SELECT 
    '权限分配验证' as check_type,
    ub.key_id as role_id,
    r.name as role_name,
    CASE 
        WHEN ub.value LIKE '%[09]%' AND ub.value LIKE '%[0901]%' AND ub.value LIKE '%[0902]%' THEN 'OK: 权限已分配'
        WHEN ub.value LIKE '%[09]%' THEN 'PARTIAL: 部分权限已分配'
        ELSE 'ERROR: 权限未分配'
    END as status,
    ub.value as permissions
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.delete_flag = '0'
ORDER BY ub.key_id;

-- =====================================================
-- 数据完整性检查脚本
-- =====================================================

-- 13. 检查菜单数据完整性
SELECT 
    '数据完整性检查' as check_type,
    COUNT(*) as total_menus,
    SUM(CASE WHEN number = '09' THEN 1 ELSE 0 END) as level1_count,
    SUM(CASE WHEN parent_number = '09' THEN 1 ELSE 0 END) as level2_count,
    CASE 
        WHEN COUNT(*) = 3 AND 
             SUM(CASE WHEN number = '09' THEN 1 ELSE 0 END) = 1 AND
             SUM(CASE WHEN parent_number = '09' THEN 1 ELSE 0 END) = 2 
        THEN 'OK: 菜单数据完整'
        ELSE 'ERROR: 菜单数据不完整'
    END as status
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0';

-- 14. 检查排序号是否正确
SELECT 
    '排序检查' as check_type,
    number,
    name,
    sort,
    CASE 
        WHEN number = '09' AND sort = '0900' THEN 'OK'
        WHEN number = '0901' AND sort = '0901' THEN 'OK'
        WHEN number = '0902' AND sort = '0902' THEN 'OK'
        ELSE 'WARNING: 排序号可能需要调整'
    END as status
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0'
ORDER BY sort;

-- =====================================================
-- 权限详细检查脚本
-- =====================================================

-- 15. 检查按钮权限配置
SELECT 
    '按钮权限检查' as check_type,
    number,
    name,
    push_btn,
    CASE 
        WHEN number = '09' AND (push_btn = '' OR push_btn IS NULL) THEN 'OK: 一级菜单无按钮权限'
        WHEN number = '0901' AND push_btn = '1,2,3,5,6,7' THEN 'OK: 按钮权限正确'
        WHEN number = '0902' AND push_btn = '1,2,3,4' THEN 'OK: 按钮权限正确'
        ELSE 'WARNING: 按钮权限配置可能需要调整'
    END as status
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0'
ORDER BY number;

-- 16. 生成权限分配建议
SELECT 
    '权限分配建议' as check_type,
    r.id as role_id,
    r.name as role_name,
    CASE 
        WHEN ub.value LIKE '%[09]%' THEN '已分配'
        ELSE '建议分配'
    END as suggestion,
    CONCAT(
        'UPDATE jsh_user_business SET value = CONCAT(IFNULL(value, \'\'), \'[09][0901][0902]\') ',
        'WHERE type = \'RoleFunctions\' AND key_id = ', r.id, ' AND delete_flag = \'0\';'
    ) as sql_command
FROM jsh_role r
LEFT JOIN jsh_user_business ub ON r.id = ub.key_id AND ub.type = 'RoleFunctions' AND ub.delete_flag = '0'
WHERE r.delete_flag = '0'
  AND r.enabled = 1
ORDER BY r.id;

-- =====================================================
-- 系统状态检查脚本
-- =====================================================

-- 17. 检查系统基本状态
SELECT 
    '系统状态检查' as check_type,
    'jsh_function' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN delete_flag = '0' THEN 1 ELSE 0 END) as active_records,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_records
FROM jsh_function
UNION ALL
SELECT 
    '系统状态检查' as check_type,
    'jsh_role' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN delete_flag = '0' THEN 1 ELSE 0 END) as active_records,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled_records
FROM jsh_role
UNION ALL
SELECT 
    '系统状态检查' as check_type,
    'jsh_user_business' as table_name,
    COUNT(*) as total_records,
    SUM(CASE WHEN delete_flag = '0' THEN 1 ELSE 0 END) as active_records,
    SUM(CASE WHEN type = 'RoleFunctions' THEN 1 ELSE 0 END) as role_function_records
FROM jsh_user_business;

-- =====================================================
-- 问题诊断脚本
-- =====================================================

-- 18. 诊断常见问题
SELECT 
    '问题诊断' as check_type,
    '菜单不显示' as problem,
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM jsh_function WHERE number = '09' AND delete_flag = '0') 
        THEN 'ERROR: 菜单未创建'
        WHEN NOT EXISTS (SELECT 1 FROM jsh_user_business WHERE value LIKE '%[09]%' AND type = 'RoleFunctions' AND delete_flag = '0')
        THEN 'ERROR: 权限未分配'
        ELSE 'OK: 配置正常'
    END as diagnosis
UNION ALL
SELECT 
    '问题诊断' as check_type,
    '页面无法访问' as problem,
    CASE 
        WHEN EXISTS (SELECT 1 FROM jsh_function WHERE number IN ('0901', '0902') AND component NOT LIKE '/inventory/%' AND delete_flag = '0')
        THEN 'ERROR: 组件路径错误'
        WHEN EXISTS (SELECT 1 FROM jsh_function WHERE number IN ('0901', '0902') AND enabled = 0 AND delete_flag = '0')
        THEN 'ERROR: 菜单未启用'
        ELSE 'OK: 配置正常'
    END as diagnosis;

-- =====================================================
-- 清理和回滚脚本（仅在需要时使用）
-- =====================================================

-- 19. 生成回滚脚本（不要直接执行）
SELECT 
    '回滚脚本' as script_type,
    CONCAT(
        '-- 删除菜单配置\n',
        'UPDATE jsh_function SET delete_flag = ''1'' WHERE number IN (''09'', ''0901'', ''0902'');\n',
        '-- 注意：权限配置需要手动编辑，移除[09][0901][0902]部分'
    ) as rollback_sql;

-- =====================================================
-- 使用说明
-- =====================================================

/*
脚本使用说明：

1. 执行前检查（脚本1-8）：
   - 在执行配置脚本前运行，了解当前状态
   - 检查编号冲突和权限配置

2. 配置后验证（脚本9-16）：
   - 在执行配置脚本后运行，验证配置是否成功
   - 检查菜单创建和权限分配

3. 系统诊断（脚本17-18）：
   - 当出现问题时运行，诊断可能的原因
   - 提供解决方案建议

4. 回滚准备（脚本19）：
   - 仅在需要回滚时参考
   - 不要直接执行，需要根据实际情况调整

注意事项：
- 所有脚本都是只读查询，不会修改数据
- 执行前请确保数据库连接正常
- 如发现问题，请参考integration-test-guide.md文档
*/
