const path = require('path')
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
const CompressionPlugin = require('compression-webpack-plugin')
const TerserPlugin = require('terser-webpack-plugin')

module.exports = {
  // 基础配置
  mode: process.env.NODE_ENV || 'development',
  entry: './src/main.ts',
  
  // 输出配置
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: process.env.NODE_ENV === 'production' 
      ? 'js/[name].[contenthash:8].js' 
      : 'js/[name].js',
    chunkFilename: process.env.NODE_ENV === 'production'
      ? 'js/[name].[contenthash:8].chunk.js'
      : 'js/[name].chunk.js',
    clean: true,
    publicPath: '/'
  },
  
  // 模块解析
  resolve: {
    extensions: ['.ts', '.js', '.vue', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'vue': 'vue/dist/vue.esm-bundler.js'
    }
  },
  
  // 模块规则
  module: {
    rules: [
      // Vue 文件
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      
      // TypeScript 文件
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        options: {
          appendTsSuffixTo: [/\.vue$/],
          transpileOnly: true
        },
        exclude: /node_modules/
      },
      
      // JavaScript 文件
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules/
      },
      
      // CSS 文件
      {
        test: /\.css$/,
        use: [
          'vue-style-loader',
          'css-loader',
          'postcss-loader'
        ]
      },
      
      // Less 文件
      {
        test: /\.less$/,
        use: [
          'vue-style-loader',
          'css-loader',
          'postcss-loader',
          'less-loader'
        ]
      },
      
      // 图片文件
      {
        test: /\.(png|jpe?g|gif|svg)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 8 * 1024 // 8KB
          }
        },
        generator: {
          filename: 'images/[name].[contenthash:8][ext]'
        }
      },
      
      // 字体文件
      {
        test: /\.(woff2?|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'fonts/[name].[contenthash:8][ext]'
        }
      }
    ]
  },
  
  // 优化配置
  optimization: {
    // 代码分割
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // 第三方库
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
          priority: 10
        },
        
        // Vue 相关
        vue: {
          test: /[\\/]node_modules[\\/](vue|@vue)[\\/]/,
          name: 'vue',
          chunks: 'all',
          priority: 20
        },
        
        // Vant UI 库
        vant: {
          test: /[\\/]node_modules[\\/]vant[\\/]/,
          name: 'vant',
          chunks: 'all',
          priority: 20
        },
        
        // 公共代码
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          priority: 5,
          reuseExistingChunk: true
        }
      }
    },
    
    // 运行时代码分离
    runtimeChunk: {
      name: 'runtime'
    },
    
    // 生产环境压缩
    minimizer: process.env.NODE_ENV === 'production' ? [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log']
          },
          format: {
            comments: false
          }
        },
        extractComments: false
      })
    ] : []
  },
  
  // 插件配置
  plugins: [
    // Vue 插件
    new (require('vue-loader')).VueLoaderPlugin(),
    
    // HTML 插件
    new (require('html-webpack-plugin'))({
      template: './public/index.html',
      filename: 'index.html',
      inject: true,
      minify: process.env.NODE_ENV === 'production' ? {
        removeComments: true,
        collapseWhitespace: true,
        removeAttributeQuotes: true
      } : false
    }),
    
    // 定义环境变量
    new (require('webpack')).DefinePlugin({
      __VUE_OPTIONS_API__: JSON.stringify(true),
      __VUE_PROD_DEVTOOLS__: JSON.stringify(false),
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
    }),
    
    // 生产环境插件
    ...(process.env.NODE_ENV === 'production' ? [
      // Gzip 压缩
      new CompressionPlugin({
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/,
        threshold: 8192,
        minRatio: 0.8
      }),
      
      // Bundle 分析器 (可选)
      ...(process.env.ANALYZE ? [
        new BundleAnalyzerPlugin({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: 'bundle-report.html'
        })
      ] : [])
    ] : [])
  ],
  
  // 开发服务器配置
  devServer: {
    host: '0.0.0.0',
    port: 8080,
    hot: true,
    open: true,
    historyApiFallback: true,
    compress: true,
    client: {
      overlay: {
        errors: true,
        warnings: false
      }
    },
    
    // 代理配置
    proxy: {
      '/api': {
        target: 'http://localhost:9999',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },
  
  // 性能配置
  performance: {
    hints: process.env.NODE_ENV === 'production' ? 'warning' : false,
    maxEntrypointSize: 512000, // 500KB
    maxAssetSize: 512000 // 500KB
  },
  
  // 开发工具
  devtool: process.env.NODE_ENV === 'production' 
    ? 'source-map' 
    : 'eval-cheap-module-source-map',
  
  // 缓存配置
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  },
  
  // 实验性功能
  experiments: {
    topLevelAwait: true
  }
}

// 开发环境特殊配置
if (process.env.NODE_ENV === 'development') {
  module.exports.plugins.push(
    // 热更新插件
    new (require('webpack')).HotModuleReplacementPlugin()
  )
}

// 性能分析配置
if (process.env.PERFORMANCE_ANALYSIS) {
  module.exports.plugins.push(
    // 速度分析插件
    new (require('speed-measure-webpack-plugin'))(),
    
    // 重复包分析插件
    new (require('duplicate-package-checker-webpack-plugin'))({
      verbose: true,
      emitError: false,
      showHelp: false
    })
  )
}
