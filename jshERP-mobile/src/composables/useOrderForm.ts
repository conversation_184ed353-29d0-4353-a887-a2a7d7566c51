/**
 * 订单表单业务逻辑Hook
 * 
 * 提取订单表单的公共业务逻辑，包括：
 * - 表单数据管理
 * - 金额计算
 * - 表单验证
 * - 数据格式化
 */

import { ref, reactive, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import type { 
  OrderForm, 
  Product, 
  ValidationRules,
  Customer,
  Salesperson,
  OrderAction
} from '@/types/order'

/**
 * 订单表单Hook
 */
export function useOrderForm(initialData?: Partial<OrderForm>) {
  // 响应式数据
  const loading = ref<boolean>(false)
  const submitting = ref<boolean>(false)
  
  // 选择器显示状态
  const showCustomerPicker = ref<boolean>(false)
  const showDatePicker = ref<boolean>(false)
  const showSalespersonPicker = ref<boolean>(false)
  const dateValue = ref<string[]>([])
  
  // 订单表单数据
  const orderForm = reactive<OrderForm>({
    orderNo: '',
    customerName: '',
    customerId: '',
    orderDate: '',
    salesperson: '',
    salespersonId: '',
    products: [],
    subtotal: 0,
    discountRate: 0,
    discountAmount: 0,
    finalAmount: 0,
    paymentAccount: '测试',
    receivedAmount: 0,
    remark: '',
    attachments: [],
    ...initialData
  })

  // 表单验证规则
  const validationRules: ValidationRules = {
    customerName: [
      { required: true, message: '请选择客户', trigger: 'blur' }
    ],
    orderDate: [
      { required: true, message: '请选择订单日期', trigger: 'blur' }
    ],
    salesperson: [
      { required: true, message: '请选择销售人员', trigger: 'blur' }
    ],
    products: [
      { 
        required: true, 
        message: '请至少添加一个商品',
        validator: (value: Product[]) => value && value.length > 0
      }
    ]
  }

  // 计算属性
  const isFormValid = computed(() => {
    return orderForm.customerName && 
           orderForm.orderDate && 
           orderForm.salesperson && 
           orderForm.products.length > 0
  })

  const totalQuantity = computed(() => {
    return orderForm.products.reduce((total, product) => total + product.quantity, 0)
  })

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number): string => {
    return amount.toFixed(2)
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 生成订单编号
   */
  const generateOrderNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `XSDD${year}${month}${day}${random}`
  }

  /**
   * 计算金额
   */
  const calculateAmount = (): void => {
    // 计算小计
    orderForm.subtotal = orderForm.products.reduce((total, product) => {
      return total + (product.price * product.quantity)
    }, 0)
    
    // 计算优惠金额
    orderForm.discountAmount = orderForm.subtotal * (orderForm.discountRate / 100)
    
    // 计算最终金额
    orderForm.finalAmount = orderForm.subtotal - orderForm.discountAmount
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const errors: string[] = []

    // 验证客户
    if (!orderForm.customerName) {
      errors.push('请选择客户')
    }

    // 验证日期
    if (!orderForm.orderDate) {
      errors.push('请选择订单日期')
    }

    // 验证销售人员
    if (!orderForm.salesperson) {
      errors.push('请选择销售人员')
    }

    // 验证商品
    if (orderForm.products.length === 0) {
      errors.push('请至少添加一个商品')
    }

    // 验证商品数量
    for (const product of orderForm.products) {
      if (product.quantity <= 0) {
        errors.push(`商品"${product.name}"的数量必须大于0`)
        break
      }
    }

    // 验证优惠率
    if (orderForm.discountRate < 0 || orderForm.discountRate > 100) {
      errors.push('优惠率必须在0-100之间')
    }

    if (errors.length > 0) {
      showToast({ type: 'fail', message: errors[0] })
      return false
    }

    return true
  }

  /**
   * 日期确认处理
   */
  const handleDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    orderForm.orderDate = formatDate(date)
    showDatePicker.value = false
  }

  /**
   * 客户选择处理
   */
  const handleCustomerSelect = (customer: Customer): void => {
    orderForm.customerName = customer.name
    orderForm.customerId = customer.id
    showCustomerPicker.value = false
  }

  /**
   * 销售人员选择处理
   */
  const handleSalespersonSelect = (salesperson: Salesperson): void => {
    orderForm.salesperson = salesperson.name
    orderForm.salespersonId = salesperson.id
    showSalespersonPicker.value = false
  }

  /**
   * 添加商品
   */
  const addProduct = (product: Product): void => {
    const existingIndex = orderForm.products.findIndex(p => p.id === product.id)
    
    if (existingIndex >= 0) {
      // 如果商品已存在，增加数量
      orderForm.products[existingIndex].quantity += product.quantity
    } else {
      // 如果商品不存在，添加新商品
      orderForm.products.push({ ...product })
    }
    
    calculateAmount()
  }

  /**
   * 移除商品
   */
  const removeProduct = async (index: number): Promise<void> => {
    try {
      await showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个商品吗？'
      })
      
      orderForm.products.splice(index, 1)
      calculateAmount()
      showToast({ type: 'success', message: '商品已删除' })
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 数量变化处理
   */
  const handleQuantityChange = (): void => {
    calculateAmount()
  }

  /**
   * 优惠率变化处理
   */
  const handleDiscountChange = (): void => {
    // 限制优惠率范围
    if (orderForm.discountRate < 0) {
      orderForm.discountRate = 0
    } else if (orderForm.discountRate > 100) {
      orderForm.discountRate = 100
    }
    
    calculateAmount()
  }

  /**
   * 文件超大小处理
   */
  const handleOversizeFile = (): void => {
    showToast({ type: 'fail', message: '文件大小不能超过1M' })
  }

  /**
   * 重置表单
   */
  const resetForm = (): void => {
    Object.assign(orderForm, {
      orderNo: generateOrderNo(),
      customerName: '',
      customerId: '',
      orderDate: formatDate(new Date()),
      salesperson: '',
      salespersonId: '',
      products: [],
      subtotal: 0,
      discountRate: 0,
      discountAmount: 0,
      finalAmount: 0,
      paymentAccount: '测试',
      receivedAmount: 0,
      remark: '',
      attachments: []
    })
  }

  /**
   * 初始化表单
   */
  const initializeForm = (data?: Partial<OrderForm>): void => {
    if (data) {
      Object.assign(orderForm, data)
    } else {
      orderForm.orderNo = generateOrderNo()
      orderForm.orderDate = formatDate(new Date())
    }
  }

  return {
    // 响应式数据
    loading,
    submitting,
    orderForm,
    showCustomerPicker,
    showDatePicker,
    showSalespersonPicker,
    dateValue,
    
    // 计算属性
    isFormValid,
    totalQuantity,
    
    // 方法
    formatCurrency,
    formatDate,
    generateOrderNo,
    calculateAmount,
    validateForm,
    handleDateConfirm,
    handleCustomerSelect,
    handleSalespersonSelect,
    addProduct,
    removeProduct,
    handleQuantityChange,
    handleDiscountChange,
    handleOversizeFile,
    resetForm,
    initializeForm,
    
    // 验证规则
    validationRules
  }
}
