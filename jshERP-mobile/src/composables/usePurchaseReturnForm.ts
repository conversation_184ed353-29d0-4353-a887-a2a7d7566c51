/**
 * 采购退货表单业务逻辑Hook
 * 
 * 基于通用业务表单逻辑，扩展采购退货特定功能：
 * - 关联采购入库单管理
 * - 退货原因和退货方式管理
 * - 退货数量控制和金额计算
 * - 退款账户选择
 */

import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from '@/composables/business/useBusinessForm'
import { DocumentType } from '@/types/business'
import type { 
  PurchaseReturnForm, 
  PurchaseProduct, 
  Purchaser,
  ReturnReason,
  ReturnType
} from '@/types/purchase'
import type { FormConfig } from '@/types/business'
import type { Supplier, Depot, Account } from '@/types/business'

/**
 * 采购退货表单Hook
 */
export function usePurchaseReturnForm(initialData?: Partial<PurchaseReturnForm>) {
  // 采购退货配置
  const purchaseReturnConfig: FormConfig = {
    formType: DocumentType.PURCHASE_RETURN,
    showPartnerSelector: true,
    partnerType: 'supplier',
    showDepotSelector: true,
    showAccountSelector: true,
    showLinkDocument: true,
    editable: true,
    defaultValues: {
      number: '',
      operTime: new Date().toISOString().slice(0, 16),
      ...initialData
    }
  }

  // 使用通用业务表单逻辑
  const businessForm = useBusinessForm(purchaseReturnConfig)
  
  // 选择器显示状态
  const showSupplierPicker = ref<boolean>(false)
  const showDatePicker = ref<boolean>(false)
  const showPurchaserPicker = ref<boolean>(false)
  const showWarehousePicker = ref<boolean>(false)
  const showInOrderPicker = ref<boolean>(false)
  const showAccountPicker = ref<boolean>(false)
  const dateValue = ref<string[]>([])
  
  // 采购退货特定的计算属性
  const purchaseReturnForm = computed(() => ({
    // 映射通用字段到采购退货字段
    returnNo: businessForm.formData.number,
    supplierName: businessForm.formData.partnerName || '',
    supplierId: businessForm.formData.partnerId || '',
    returnDate: businessForm.formData.operTime,
    purchaser: businessForm.formData.otherField1 || '',
    purchaserId: businessForm.formData.otherField2 || '',
    
    // 关联信息
    relatedInNo: businessForm.formData.linkNumber || '',
    relatedInId: businessForm.formData.linkId || '',
    
    // 退货信息
    returnReason: businessForm.formData.otherField3 as ReturnReason || 'quality_issue',
    returnType: businessForm.formData.otherField4 as ReturnType || 'direct_return',
    
    // 商品信息
    products: businessForm.formData.details.map(detail => ({
      id: detail.productId,
      name: detail.productName,
      spec: detail.otherField1 || '',
      originalQuantity: detail.otherField2 ? Number(detail.otherField2) : 0,
      maxReturnQuantity: detail.otherField3 ? Number(detail.otherField3) : 0,
      returnQuantity: detail.basicNumber || 0,
      returnPrice: detail.unitPrice || 0,
      returnReason: detail.otherField4 as ReturnReason || 'quality_issue',
      supplierProductCode: detail.otherField5 || ''
    })) as PurchaseProduct[],
    
    // 金额信息
    subtotal: businessForm.formData.totalPrice || 0,
    discountRate: businessForm.formData.otherField6 ? Number(businessForm.formData.otherField6) : 0,
    discountAmount: 0, // 计算得出
    finalAmount: businessForm.totalAmount.value,
    
    // 仓库信息
    warehouseId: businessForm.formData.depotId || '',
    warehouseName: businessForm.formData.depotName || '',
    
    // 退款信息
    refundAccountId: businessForm.formData.accountId || '',
    refundAccountName: businessForm.formData.accountName || '',
    
    // 附加信息
    remark: businessForm.formData.remark || '',
    attachments: []
  }))

  // 计算属性
  const isFormValid = computed(() => {
    const form = purchaseReturnForm.value
    return form.supplierName && 
           form.returnDate && 
           form.purchaser && 
           form.warehouseName &&
           form.refundAccountName &&
           form.products.length > 0
  })

  const totalReturnQuantity = computed(() => {
    return purchaseReturnForm.value.products.reduce((total, product) => total + product.returnQuantity, 0)
  })

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number): string => {
    return amount.toFixed(2)
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 生成采购退货编号
   */
  const generateReturnNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `CGTH${year}${month}${day}${random}`
  }

  /**
   * 计算金额
   */
  const calculateAmount = (): void => {
    const form = purchaseReturnForm.value
    
    // 计算小计
    const subtotal = form.products.reduce((total, product) => {
      return total + (product.returnPrice * product.returnQuantity)
    }, 0)
    
    // 更新业务表单的总价
    businessForm.formData.totalPrice = subtotal
    
    // 计算优惠金额
    const discountAmount = subtotal * (form.discountRate / 100)
    
    // 计算最终金额
    const finalAmount = subtotal - discountAmount
    
    // 更新相关字段
    businessForm.formData.otherField6 = form.discountRate.toString()
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const form = purchaseReturnForm.value
    const errors: string[] = []

    // 验证供应商
    if (!form.supplierName) {
      errors.push('请选择供应商')
    }

    // 验证日期
    if (!form.returnDate) {
      errors.push('请选择退货日期')
    }

    // 验证采购员
    if (!form.purchaser) {
      errors.push('请选择采购员')
    }

    // 验证仓库
    if (!form.warehouseName) {
      errors.push('请选择出库仓库')
    }

    // 验证退款账户
    if (!form.refundAccountName) {
      errors.push('请选择退款账户')
    }

    // 验证商品
    if (form.products.length === 0) {
      errors.push('请至少添加一个退货商品')
    }

    // 验证退货数量
    for (const product of form.products) {
      if (product.returnQuantity <= 0) {
        errors.push(`商品"${product.name}"的退货数量必须大于0`)
        break
      }
      
      if (product.returnQuantity > product.maxReturnQuantity) {
        errors.push(`商品"${product.name}"的退货数量不能超过可退数量${product.maxReturnQuantity}`)
        break
      }
    }

    // 验证优惠率
    if (form.discountRate < 0 || form.discountRate > 100) {
      errors.push('优惠率必须在0-100之间')
    }

    if (errors.length > 0) {
      showToast({ type: 'fail', message: errors[0] })
      return false
    }

    return true
  }

  /**
   * 日期确认处理
   */
  const handleDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    businessForm.formData.operTime = formatDate(date)
    showDatePicker.value = false
  }

  /**
   * 供应商选择处理
   */
  const handleSupplierSelect = (supplier: Supplier): void => {
    businessForm.formData.partnerName = supplier.name
    businessForm.formData.partnerId = supplier.id
    showSupplierPicker.value = false
  }

  /**
   * 采购员选择处理
   */
  const handlePurchaserSelect = (purchaser: Purchaser): void => {
    businessForm.formData.otherField1 = purchaser.name
    businessForm.formData.otherField2 = purchaser.id.toString()
    showPurchaserPicker.value = false
  }

  /**
   * 仓库选择处理
   */
  const handleWarehouseSelect = (warehouse: Depot): void => {
    businessForm.formData.depotName = warehouse.name
    businessForm.formData.depotId = warehouse.id
    showWarehousePicker.value = false
  }

  /**
   * 退款账户选择处理
   */
  const handleAccountSelect = (account: Account): void => {
    businessForm.formData.accountName = account.name
    businessForm.formData.accountId = account.id
    showAccountPicker.value = false
  }

  /**
   * 采购入库单选择处理
   */
  const handleInOrderSelect = (inOrder: any): void => {
    businessForm.formData.linkNumber = inOrder.inNo
    businessForm.formData.linkId = inOrder.id
    
    // 自动填充供应商信息
    businessForm.formData.partnerName = inOrder.supplierName
    businessForm.formData.partnerId = inOrder.supplierId
    businessForm.formData.otherField1 = inOrder.purchaser
    businessForm.formData.otherField2 = inOrder.purchaserId.toString()
    businessForm.formData.depotName = inOrder.warehouseName
    businessForm.formData.depotId = inOrder.warehouseId
    
    // 自动填充商品信息
    businessForm.formData.details = inOrder.products.map((product: any) => ({
      productId: product.id,
      productName: product.name,
      unitPrice: product.purchasePrice,
      basicNumber: 0, // 退货数量默认为0
      otherField1: product.spec || '',
      otherField2: product.quantity.toString(), // 原入库数量
      otherField3: product.quantity.toString(), // 最大可退数量（简化处理）
      otherField4: 'quality_issue', // 默认退货原因
      otherField5: product.supplierProductCode || ''
    }))
    
    calculateAmount()
    showInOrderPicker.value = false
  }

  /**
   * 添加退货商品
   */
  const addReturnProduct = (product: PurchaseProduct): void => {
    businessForm.addDetailItem({
      productId: product.id,
      productName: product.name,
      unitPrice: product.returnPrice,
      basicNumber: product.returnQuantity,
      otherField1: product.spec || '',
      otherField2: product.originalQuantity.toString(),
      otherField3: product.maxReturnQuantity.toString(),
      otherField4: product.returnReason || 'quality_issue',
      otherField5: product.supplierProductCode || ''
    })
    
    calculateAmount()
  }

  /**
   * 移除退货商品
   */
  const removeReturnProduct = async (index: number): Promise<void> => {
    try {
      await showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个退货商品吗？'
      })
      
      businessForm.removeDetailItem(index)
      calculateAmount()
      showToast({ type: 'success', message: '商品已删除' })
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 退货数量变化处理
   */
  const handleReturnQuantityChange = (index: number, quantity: number): void => {
    if (businessForm.formData.details[index]) {
      const maxQuantity = Number(businessForm.formData.details[index].otherField3)
      
      if (quantity > maxQuantity) {
        showToast({ type: 'fail', message: `退货数量不能超过${maxQuantity}` })
        businessForm.formData.details[index].basicNumber = maxQuantity
      } else {
        businessForm.formData.details[index].basicNumber = quantity
      }
      
      calculateAmount()
    }
  }

  /**
   * 退货单价变化处理
   */
  const handleReturnPriceChange = (index: number, price: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].unitPrice = price
      calculateAmount()
    }
  }

  /**
   * 退货原因变化处理
   */
  const handleReturnReasonChange = (index: number, reason: ReturnReason): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].otherField4 = reason
    }
  }

  /**
   * 优惠率变化处理
   */
  const handleDiscountChange = (): void => {
    const form = purchaseReturnForm.value
    
    // 限制优惠率范围
    if (form.discountRate < 0) {
      businessForm.formData.otherField6 = '0'
    } else if (form.discountRate > 100) {
      businessForm.formData.otherField6 = '100'
    }
    
    calculateAmount()
  }

  /**
   * 重置表单
   */
  const resetForm = (): void => {
    businessForm.resetForm()
    businessForm.formData.number = generateReturnNo()
    businessForm.formData.operTime = formatDate(new Date())
  }

  /**
   * 初始化表单
   */
  const initializeForm = (data?: Partial<PurchaseReturnForm>): void => {
    if (data) {
      // 映射数据到业务表单
      businessForm.formData.number = data.returnNo || generateReturnNo()
      businessForm.formData.partnerName = data.supplierName || ''
      businessForm.formData.partnerId = data.supplierId || ''
      businessForm.formData.operTime = data.returnDate || formatDate(new Date())
      businessForm.formData.otherField1 = data.purchaser || ''
      businessForm.formData.otherField2 = data.purchaserId?.toString() || ''
      businessForm.formData.depotName = data.warehouseName || ''
      businessForm.formData.depotId = data.warehouseId || ''
      businessForm.formData.accountName = data.refundAccountName || ''
      businessForm.formData.accountId = data.refundAccountId || ''
      businessForm.formData.linkNumber = data.relatedInNo || ''
      businessForm.formData.linkId = data.relatedInId || ''
      businessForm.formData.otherField3 = data.returnReason || 'quality_issue'
      businessForm.formData.otherField4 = data.returnType || 'direct_return'
      businessForm.formData.remark = data.remark || ''
      
      // 映射商品数据
      if (data.products) {
        businessForm.formData.details = data.products.map(product => ({
          productId: product.id,
          productName: product.name,
          unitPrice: product.returnPrice,
          basicNumber: product.returnQuantity,
          otherField1: product.spec || '',
          otherField2: product.originalQuantity.toString(),
          otherField3: product.maxReturnQuantity.toString(),
          otherField4: product.returnReason || 'quality_issue',
          otherField5: product.supplierProductCode || ''
        }))
      }
    } else {
      businessForm.formData.number = generateReturnNo()
      businessForm.formData.operTime = formatDate(new Date())
    }
  }

  return {
    // 响应式数据
    loading: businessForm.loading,
    submitting: businessForm.submitting,
    purchaseReturnForm,
    showSupplierPicker,
    showDatePicker,
    showPurchaserPicker,
    showWarehousePicker,
    showInOrderPicker,
    showAccountPicker,
    dateValue,
    
    // 计算属性
    isFormValid,
    totalReturnQuantity,
    
    // 业务表单方法
    ...businessForm,
    
    // 采购退货特定方法
    formatCurrency,
    formatDate,
    generateReturnNo,
    calculateAmount,
    validateForm,
    handleDateConfirm,
    handleSupplierSelect,
    handlePurchaserSelect,
    handleWarehouseSelect,
    handleAccountSelect,
    handleInOrderSelect,
    addReturnProduct,
    removeReturnProduct,
    handleReturnQuantityChange,
    handleReturnPriceChange,
    handleReturnReasonChange,
    handleDiscountChange,
    resetForm,
    initializeForm
  }
}
