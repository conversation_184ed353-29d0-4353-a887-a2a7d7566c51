/**
 * 通用业务表单逻辑 Composable
 * 
 * 提供所有业务模块共享的表单管理功能
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import type { 
  BaseDocumentHeader, 
  BaseDetailItem, 
  FormConfig, 
  DocumentType,
  DocumentStatus,
  ApiResponse,
  OperationResult
} from '@/types/business'

// 表单数据接口
export interface BusinessFormData extends BaseDocumentHeader {
  details: BaseDetailItem[]
}

// 表单状态接口
export interface FormState {
  loading: boolean
  saving: boolean
  submitting: boolean
  validating: boolean
  dirty: boolean
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  validator?: (value: any) => boolean | string
}

export interface ValidationRules {
  [key: string]: ValidationRule[]
}

/**
 * 通用业务表单 Hook
 */
export function useBusinessForm(config: FormConfig) {
  // 表单数据
  const formData = reactive<BusinessFormData>({
    type: config.formType,
    subType: '',
    number: '',
    operTime: new Date().toISOString().slice(0, 16),
    totalPrice: 0,
    status: DocumentStatus.DRAFT,
    details: [],
    ...config.defaultValues
  })

  // 表单状态
  const formState = reactive<FormState>({
    loading: false,
    saving: false,
    submitting: false,
    validating: false,
    dirty: false
  })

  // 验证错误
  const validationErrors = ref<Record<string, string>>({})

  // 是否可编辑
  const isEditable = computed(() => {
    return config.editable !== false && formData.status === DocumentStatus.DRAFT
  })

  // 是否有未保存的更改
  const hasUnsavedChanges = computed(() => formState.dirty)

  // 表单总金额
  const totalAmount = computed(() => {
    return formData.details.reduce((sum, item) => {
      return sum + (item.allPrice || 0)
    }, 0)
  })

  // 明细项数量
  const detailCount = computed(() => formData.details.length)

  /**
   * 初始化表单
   */
  const initializeForm = (initialData?: Partial<BusinessFormData>) => {
    if (initialData) {
      Object.assign(formData, initialData)
    }
    
    // 生成单据编号
    if (!formData.number) {
      generateDocumentNumber()
    }
    
    // 重置状态
    formState.dirty = false
    validationErrors.value = {}
  }

  /**
   * 生成单据编号
   */
  const generateDocumentNumber = async () => {
    try {
      // 这里应该调用API生成单据编号
      const prefix = getDocumentPrefix(config.formType)
      const timestamp = Date.now().toString().slice(-6)
      formData.number = `${prefix}${timestamp}`
    } catch (error) {
      console.error('生成单据编号失败:', error)
    }
  }

  /**
   * 获取单据前缀
   */
  const getDocumentPrefix = (type: DocumentType): string => {
    const prefixMap: Record<DocumentType, string> = {
      [DocumentType.SALES_ORDER]: 'XSDD',
      [DocumentType.SALES_OUT]: 'XSCK',
      [DocumentType.SALES_RETURN]: 'XSTH',
      [DocumentType.RETAIL_OUT]: 'LSCK',
      [DocumentType.RETAIL_RETURN]: 'LSTH',
      [DocumentType.PURCHASE_ORDER]: 'CGDD',
      [DocumentType.PURCHASE_IN]: 'CGRK',
      [DocumentType.PURCHASE_RETURN]: 'CGTH',
      [DocumentType.OTHER_IN]: 'QTRK',
      [DocumentType.OTHER_OUT]: 'QTCK',
      [DocumentType.TRANSFER_OUT]: 'DBCK',
      [DocumentType.ASSEMBLY]: 'ZZ',
      [DocumentType.DISASSEMBLY]: 'CX',
      [DocumentType.INVENTORY_CHECK]: 'PD',
      [DocumentType.RECEIPT]: 'SK',
      [DocumentType.PAYMENT]: 'FK',
      [DocumentType.TRANSFER]: 'ZZ',
      [DocumentType.INCOME]: 'SR',
      [DocumentType.EXPENSE]: 'ZC'
    }
    return prefixMap[type] || 'DOC'
  }

  /**
   * 添加明细项
   */
  const addDetailItem = (item: Partial<BaseDetailItem>) => {
    const newItem: BaseDetailItem = {
      productId: '',
      productName: '',
      basicNumber: 1,
      unitPrice: 0,
      allPrice: 0,
      ...item
    }
    
    formData.details.push(newItem)
    markAsDirty()
    calculateTotalAmount()
  }

  /**
   * 删除明细项
   */
  const removeDetailItem = (index: number) => {
    if (index >= 0 && index < formData.details.length) {
      formData.details.splice(index, 1)
      markAsDirty()
      calculateTotalAmount()
    }
  }

  /**
   * 更新明细项
   */
  const updateDetailItem = (index: number, updates: Partial<BaseDetailItem>) => {
    if (index >= 0 && index < formData.details.length) {
      Object.assign(formData.details[index], updates)
      
      // 重新计算该项金额
      const item = formData.details[index]
      if (item.basicNumber && item.unitPrice) {
        item.allPrice = item.basicNumber * item.unitPrice
      }
      
      markAsDirty()
      calculateTotalAmount()
    }
  }

  /**
   * 计算总金额
   */
  const calculateTotalAmount = () => {
    formData.totalPrice = totalAmount.value
  }

  /**
   * 标记为已修改
   */
  const markAsDirty = () => {
    formState.dirty = true
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    validationErrors.value = {}
    let isValid = true

    // 基础字段验证
    if (!formData.number?.trim()) {
      validationErrors.value.number = '单据编号不能为空'
      isValid = false
    }

    if (!formData.operTime) {
      validationErrors.value.operTime = '操作时间不能为空'
      isValid = false
    }

    // 往来单位验证
    if (config.showPartnerSelector && !formData.partnerId) {
      const partnerLabel = config.partnerType === 'customer' ? '客户' : '供应商'
      validationErrors.value.partnerId = `${partnerLabel}不能为空`
      isValid = false
    }

    // 明细项验证
    if (formData.details.length === 0) {
      validationErrors.value.details = '至少需要添加一个商品'
      isValid = false
    }

    // 验证每个明细项
    formData.details.forEach((item, index) => {
      if (!item.productId) {
        validationErrors.value[`details.${index}.productId`] = '商品不能为空'
        isValid = false
      }
      
      if (!item.basicNumber || item.basicNumber <= 0) {
        validationErrors.value[`details.${index}.basicNumber`] = '数量必须大于0'
        isValid = false
      }
      
      if (!item.unitPrice || item.unitPrice < 0) {
        validationErrors.value[`details.${index}.unitPrice`] = '单价不能为负数'
        isValid = false
      }
    })

    return isValid
  }

  /**
   * 保存表单
   */
  const saveForm = async (): Promise<OperationResult> => {
    if (!validateForm()) {
      return {
        success: false,
        message: '表单验证失败，请检查输入'
      }
    }

    formState.saving = true

    try {
      // 这里应该调用API保存数据
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
      
      formState.dirty = false
      showToast('保存成功')
      
      return {
        success: true,
        message: '保存成功'
      }
    } catch (error) {
      console.error('保存失败:', error)
      return {
        success: false,
        message: '保存失败，请重试'
      }
    } finally {
      formState.saving = false
    }
  }

  /**
   * 提交表单
   */
  const submitForm = async (): Promise<OperationResult> => {
    if (!validateForm()) {
      return {
        success: false,
        message: '表单验证失败，请检查输入'
      }
    }

    formState.submitting = true

    try {
      // 这里应该调用API提交数据
      await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
      
      formData.status = DocumentStatus.PENDING
      formState.dirty = false
      showToast('提交成功')
      
      return {
        success: true,
        message: '提交成功'
      }
    } catch (error) {
      console.error('提交失败:', error)
      return {
        success: false,
        message: '提交失败，请重试'
      }
    } finally {
      formState.submitting = false
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      type: config.formType,
      subType: '',
      number: '',
      operTime: new Date().toISOString().slice(0, 16),
      totalPrice: 0,
      status: DocumentStatus.DRAFT,
      details: [],
      ...config.defaultValues
    })
    
    formState.dirty = false
    validationErrors.value = {}
    generateDocumentNumber()
  }

  /**
   * 检查是否可以离开页面
   */
  const canLeave = async (): Promise<boolean> => {
    if (!hasUnsavedChanges.value) {
      return true
    }

    try {
      await showConfirmDialog({
        title: '提示',
        message: '您有未保存的更改，确定要离开吗？'
      })
      return true
    } catch {
      return false
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    initializeForm()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    // 清理工作
  })

  return {
    // 数据
    formData,
    formState,
    validationErrors,
    
    // 计算属性
    isEditable,
    hasUnsavedChanges,
    totalAmount,
    detailCount,
    
    // 方法
    initializeForm,
    generateDocumentNumber,
    addDetailItem,
    removeDetailItem,
    updateDetailItem,
    calculateTotalAmount,
    markAsDirty,
    validateForm,
    saveForm,
    submitForm,
    resetForm,
    canLeave
  }
}
