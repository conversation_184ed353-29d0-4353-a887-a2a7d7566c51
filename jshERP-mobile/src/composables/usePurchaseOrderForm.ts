/**
 * 采购订单表单业务逻辑Hook
 * 
 * 基于通用业务表单逻辑，扩展采购订单特定功能：
 * - 供应商和采购员管理
 * - 采购订单特定的金额计算（含税计算）
 * - 采购订单特定的验证规则
 * - 交货期和付款条件管理
 */

import { ref, computed } from 'vue'
import { showToast, showConfirmDialog } from 'vant'
import { useBusinessForm } from '@/composables/business/useBusinessForm'
import { DocumentType } from '@/types/business'
import type { 
  PurchaseOrderForm, 
  PurchaseProduct, 
  Purchaser,
  PurchaseAction
} from '@/types/purchase'
import type { FormConfig } from '@/types/business'
import type { Supplier } from '@/types/business'

/**
 * 采购订单表单Hook
 */
export function usePurchaseOrderForm(initialData?: Partial<PurchaseOrderForm>) {
  // 采购订单配置
  const purchaseOrderConfig: FormConfig = {
    formType: DocumentType.PURCHASE_ORDER,
    showPartnerSelector: true,
    partnerType: 'supplier',
    showDepotSelector: true,
    showAccountSelector: true,
    showLinkDocument: false,
    editable: true,
    defaultValues: {
      number: '',
      operTime: new Date().toISOString().slice(0, 16),
      ...initialData
    }
  }

  // 使用通用业务表单逻辑
  const businessForm = useBusinessForm(purchaseOrderConfig)
  
  // 选择器显示状态
  const showSupplierPicker = ref<boolean>(false)
  const showDatePicker = ref<boolean>(false)
  const showPurchaserPicker = ref<boolean>(false)
  const showDeliveryDatePicker = ref<boolean>(false)
  const dateValue = ref<string[]>([])
  const deliveryDateValue = ref<string[]>([])
  
  // 采购订单特定的计算属性
  const purchaseOrderForm = computed(() => ({
    // 映射通用字段到采购订单字段
    orderNo: businessForm.formData.number,
    supplierName: businessForm.formData.partnerName || '',
    supplierId: businessForm.formData.partnerId || '',
    orderDate: businessForm.formData.operTime,
    purchaser: businessForm.formData.otherField1 || '',
    purchaserId: businessForm.formData.otherField2 || '',
    
    // 商品信息
    products: businessForm.formData.details.map(detail => ({
      id: detail.productId,
      name: detail.productName,
      spec: detail.otherField1 || '',
      quantity: detail.basicNumber || 0,
      purchasePrice: detail.unitPrice || 0,
      supplierProductCode: detail.otherField2 || '',
      deliveryDays: detail.otherField3 ? Number(detail.otherField3) : undefined
    })) as PurchaseProduct[],
    
    // 金额信息
    subtotal: businessForm.formData.totalPrice || 0,
    discountRate: businessForm.formData.otherField3 ? Number(businessForm.formData.otherField3) : 0,
    discountAmount: 0, // 计算得出
    taxRate: businessForm.formData.otherField4 ? Number(businessForm.formData.otherField4) : 0,
    taxAmount: 0, // 计算得出
    finalAmount: businessForm.totalAmount.value,
    
    // 交货信息
    expectedDeliveryDate: businessForm.formData.otherField5 || '',
    deliveryAddress: businessForm.formData.otherField6 || '',
    contactPerson: businessForm.formData.otherField7 || '',
    contactPhone: businessForm.formData.otherField8 || '',
    
    // 结算信息
    paymentAccount: businessForm.formData.accountName || '',
    paymentTerms: businessForm.formData.otherField9 || '',
    prepaidAmount: businessForm.formData.otherField10 ? Number(businessForm.formData.otherField10) : 0,
    
    // 附加信息
    remark: businessForm.formData.remark || '',
    attachments: []
  }))

  // 计算属性
  const isFormValid = computed(() => {
    const form = purchaseOrderForm.value
    return form.supplierName && 
           form.orderDate && 
           form.purchaser && 
           form.products.length > 0
  })

  const totalQuantity = computed(() => {
    return purchaseOrderForm.value.products.reduce((total, product) => total + product.quantity, 0)
  })

  /**
   * 格式化货币
   */
  const formatCurrency = (amount: number): string => {
    return amount.toFixed(2)
  }

  /**
   * 格式化日期
   */
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * 生成采购订单编号
   */
  const generateOrderNo = (): string => {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    return `CGDD${year}${month}${day}${random}`
  }

  /**
   * 计算金额（含税计算）
   */
  const calculateAmount = (): void => {
    const form = purchaseOrderForm.value
    
    // 计算小计
    const subtotal = form.products.reduce((total, product) => {
      return total + (product.purchasePrice * product.quantity)
    }, 0)
    
    // 更新业务表单的总价
    businessForm.formData.totalPrice = subtotal
    
    // 计算优惠金额
    const discountAmount = subtotal * (form.discountRate / 100)
    
    // 计算税额
    const taxableAmount = subtotal - discountAmount
    const taxAmount = taxableAmount * (form.taxRate / 100)
    
    // 计算最终金额
    const finalAmount = taxableAmount + taxAmount
    
    // 更新相关字段
    businessForm.formData.otherField3 = form.discountRate.toString()
    businessForm.formData.otherField4 = form.taxRate.toString()
  }

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const form = purchaseOrderForm.value
    const errors: string[] = []

    // 验证供应商
    if (!form.supplierName) {
      errors.push('请选择供应商')
    }

    // 验证日期
    if (!form.orderDate) {
      errors.push('请选择订单日期')
    }

    // 验证采购员
    if (!form.purchaser) {
      errors.push('请选择采购员')
    }

    // 验证商品
    if (form.products.length === 0) {
      errors.push('请至少添加一个商品')
    }

    // 验证商品数量
    for (const product of form.products) {
      if (product.quantity <= 0) {
        errors.push(`商品"${product.name}"的数量必须大于0`)
        break
      }
    }

    // 验证优惠率
    if (form.discountRate < 0 || form.discountRate > 100) {
      errors.push('优惠率必须在0-100之间')
    }

    // 验证税率
    if (form.taxRate && (form.taxRate < 0 || form.taxRate > 100)) {
      errors.push('税率必须在0-100之间')
    }

    // 验证交货期
    if (form.expectedDeliveryDate) {
      const deliveryDate = new Date(form.expectedDeliveryDate)
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      
      if (deliveryDate < today) {
        errors.push('交货日期不能早于今天')
      }
    }

    if (errors.length > 0) {
      showToast({ type: 'fail', message: errors[0] })
      return false
    }

    return true
  }

  /**
   * 日期确认处理
   */
  const handleDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    businessForm.formData.operTime = formatDate(date)
    showDatePicker.value = false
  }

  /**
   * 交货日期确认处理
   */
  const handleDeliveryDateConfirm = (value: string[]): void => {
    const date = new Date(value.join('-'))
    businessForm.formData.otherField5 = formatDate(date)
    showDeliveryDatePicker.value = false
  }

  /**
   * 供应商选择处理
   */
  const handleSupplierSelect = (supplier: Supplier): void => {
    businessForm.formData.partnerName = supplier.name
    businessForm.formData.partnerId = supplier.id
    showSupplierPicker.value = false
  }

  /**
   * 采购员选择处理
   */
  const handlePurchaserSelect = (purchaser: Purchaser): void => {
    businessForm.formData.otherField1 = purchaser.name
    businessForm.formData.otherField2 = purchaser.id.toString()
    showPurchaserPicker.value = false
  }

  /**
   * 添加商品
   */
  const addProduct = (product: PurchaseProduct): void => {
    businessForm.addDetailItem({
      productId: product.id,
      productName: product.name,
      unitPrice: product.purchasePrice,
      basicNumber: product.quantity,
      otherField1: product.spec || '',
      otherField2: product.supplierProductCode || '',
      otherField3: product.deliveryDays?.toString() || ''
    })
    
    calculateAmount()
  }

  /**
   * 移除商品
   */
  const removeProduct = async (index: number): Promise<void> => {
    try {
      await showConfirmDialog({
        title: '确认删除',
        message: '确定要删除这个商品吗？'
      })
      
      businessForm.removeDetailItem(index)
      calculateAmount()
      showToast({ type: 'success', message: '商品已删除' })
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 数量变化处理
   */
  const handleQuantityChange = (index: number, quantity: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].basicNumber = quantity
      calculateAmount()
    }
  }

  /**
   * 单价变化处理
   */
  const handlePriceChange = (index: number, price: number): void => {
    if (businessForm.formData.details[index]) {
      businessForm.formData.details[index].unitPrice = price
      calculateAmount()
    }
  }

  /**
   * 优惠率变化处理
   */
  const handleDiscountChange = (): void => {
    const form = purchaseOrderForm.value
    
    // 限制优惠率范围
    if (form.discountRate < 0) {
      businessForm.formData.otherField3 = '0'
    } else if (form.discountRate > 100) {
      businessForm.formData.otherField3 = '100'
    }
    
    calculateAmount()
  }

  /**
   * 税率变化处理
   */
  const handleTaxRateChange = (): void => {
    const form = purchaseOrderForm.value
    
    // 限制税率范围
    if (form.taxRate && form.taxRate < 0) {
      businessForm.formData.otherField4 = '0'
    } else if (form.taxRate && form.taxRate > 100) {
      businessForm.formData.otherField4 = '100'
    }
    
    calculateAmount()
  }

  /**
   * 重置表单
   */
  const resetForm = (): void => {
    businessForm.resetForm()
    businessForm.formData.number = generateOrderNo()
    businessForm.formData.operTime = formatDate(new Date())
  }

  /**
   * 初始化表单
   */
  const initializeForm = (data?: Partial<PurchaseOrderForm>): void => {
    if (data) {
      // 映射数据到业务表单
      businessForm.formData.number = data.orderNo || generateOrderNo()
      businessForm.formData.partnerName = data.supplierName || ''
      businessForm.formData.partnerId = data.supplierId || ''
      businessForm.formData.operTime = data.orderDate || formatDate(new Date())
      businessForm.formData.otherField1 = data.purchaser || ''
      businessForm.formData.otherField2 = data.purchaserId?.toString() || ''
      businessForm.formData.remark = data.remark || ''
      
      // 映射商品数据
      if (data.products) {
        businessForm.formData.details = data.products.map(product => ({
          productId: product.id,
          productName: product.name,
          unitPrice: product.purchasePrice,
          basicNumber: product.quantity,
          otherField1: product.spec || '',
          otherField2: product.supplierProductCode || '',
          otherField3: product.deliveryDays?.toString() || ''
        }))
      }
    } else {
      businessForm.formData.number = generateOrderNo()
      businessForm.formData.operTime = formatDate(new Date())
    }
  }

  return {
    // 响应式数据
    loading: businessForm.loading,
    submitting: businessForm.submitting,
    purchaseOrderForm,
    showSupplierPicker,
    showDatePicker,
    showPurchaserPicker,
    showDeliveryDatePicker,
    dateValue,
    deliveryDateValue,
    
    // 计算属性
    isFormValid,
    totalQuantity,
    
    // 业务表单方法
    ...businessForm,
    
    // 采购订单特定方法
    formatCurrency,
    formatDate,
    generateOrderNo,
    calculateAmount,
    validateForm,
    handleDateConfirm,
    handleDeliveryDateConfirm,
    handleSupplierSelect,
    handlePurchaserSelect,
    addProduct,
    removeProduct,
    handleQuantityChange,
    handlePriceChange,
    handleDiscountChange,
    handleTaxRateChange,
    resetForm,
    initializeForm
  }
}
