/**
 * 销售订单相关类型定义
 * 
 * 统一管理订单相关的TypeScript类型，确保类型安全和一致性
 */

/**
 * 商品接口
 */
export interface Product {
  /** 商品ID */
  id: string
  /** 商品名称 */
  name: string
  /** 商品规格 */
  spec: string
  /** 商品单价 */
  price: number
  /** 商品数量 */
  quantity: number
  /** 商品条码（可选） */
  barcode?: string
  /** 商品单位（可选） */
  unit?: string
  /** 商品库存（可选） */
  stock?: number
}

/**
 * 订单表单接口
 */
export interface OrderForm {
  /** 订单编号 */
  orderNo: string
  /** 客户名称 */
  customerName: string
  /** 客户ID（可选） */
  customerId?: string
  /** 订单日期 */
  orderDate: string
  /** 销售人员 */
  salesperson: string
  /** 销售人员ID（可选） */
  salespersonId?: string
  /** 商品列表 */
  products: Product[]
  /** 小计金额 */
  subtotal: number
  /** 优惠率 */
  discountRate: number
  /** 优惠金额 */
  discountAmount: number
  /** 最终金额 */
  finalAmount: number
  /** 结算账户 */
  paymentAccount: string
  /** 收取订金 */
  receivedAmount: number
  /** 备注 */
  remark: string
  /** 附件列表 */
  attachments: AttachmentFile[]
  /** 订单状态（可选） */
  status?: OrderStatus
  /** 创建时间（可选） */
  createTime?: string
  /** 更新时间（可选） */
  updateTime?: string
}

/**
 * 附件文件接口
 */
export interface AttachmentFile {
  /** 文件ID */
  id?: string
  /** 文件名 */
  name: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: string
  /** 文件URL */
  url?: string
  /** 上传状态 */
  status?: 'uploading' | 'success' | 'error'
}

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  /** 草稿 */
  DRAFT = 'draft',
  /** 待审核 */
  PENDING = 'pending',
  /** 已审核 */
  APPROVED = 'approved',
  /** 已发货 */
  SHIPPED = 'shipped',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已取消 */
  CANCELLED = 'cancelled'
}

/**
 * 客户信息接口
 */
export interface Customer {
  /** 客户ID */
  id: string
  /** 客户名称 */
  name: string
  /** 客户编码 */
  code?: string
  /** 联系电话 */
  phone?: string
  /** 客户地址 */
  address?: string
  /** 客户类型 */
  type?: string
}

/**
 * 销售人员接口
 */
export interface Salesperson {
  /** 销售人员ID */
  id: string
  /** 销售人员姓名 */
  name: string
  /** 销售人员编码 */
  code?: string
  /** 部门 */
  department?: string
  /** 联系电话 */
  phone?: string
}

/**
 * 表单验证规则接口
 */
export interface ValidationRule {
  /** 是否必填 */
  required?: boolean
  /** 错误提示信息 */
  message?: string
  /** 自定义验证函数 */
  validator?: (value: any) => boolean | string
  /** 触发验证的时机 */
  trigger?: 'blur' | 'change'
}

/**
 * 表单验证规则集合
 */
export interface ValidationRules {
  [key: string]: ValidationRule[]
}

/**
 * 订单操作类型
 */
export enum OrderAction {
  /** 保存 */
  SAVE = 'save',
  /** 保存并提交 */
  SAVE_AND_SUBMIT = 'save_and_submit',
  /** 更新 */
  UPDATE = 'update',
  /** 删除 */
  DELETE = 'delete',
  /** 审核 */
  APPROVE = 'approve',
  /** 拒绝 */
  REJECT = 'reject'
}

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  /** 响应状态码 */
  code: number
  /** 响应消息 */
  message: string
  /** 响应数据 */
  data: T
  /** 是否成功 */
  success: boolean
  /** 时间戳 */
  timestamp?: number
}

/**
 * 分页参数接口
 */
export interface PaginationParams {
  /** 当前页码 */
  current: number
  /** 每页大小 */
  size: number
  /** 总记录数 */
  total?: number
}

/**
 * 订单查询参数接口
 */
export interface OrderQueryParams extends PaginationParams {
  /** 订单编号 */
  orderNo?: string
  /** 客户名称 */
  customerName?: string
  /** 订单状态 */
  status?: OrderStatus
  /** 开始日期 */
  startDate?: string
  /** 结束日期 */
  endDate?: string
  /** 销售人员 */
  salesperson?: string
}
