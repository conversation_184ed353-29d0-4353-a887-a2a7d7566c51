<template>
  <div class="base-business-form">
    <!-- 表单头部信息 -->
    <div class="form-header">
      <van-cell-group inset>
        <!-- 单据编号 -->
        <van-field
          v-model="formData.number"
          label="单据编号"
          placeholder="自动生成"
          readonly
          :border="false"
        />
        
        <!-- 操作时间 -->
        <van-field
          v-model="formData.operTime"
          label="操作时间"
          placeholder="请选择时间"
          readonly
          :border="false"
          @click="showDatePicker = true"
        />
        
        <!-- 往来单位选择 -->
        <van-field
          v-if="config.showPartnerSelector"
          v-model="partnerDisplayName"
          :label="partnerLabel"
          :placeholder="`请选择${partnerLabel}`"
          readonly
          :border="false"
          :error="!!validationErrors.partnerId"
          :error-message="validationErrors.partnerId"
          @click="showPartnerSelector"
        />
        
        <!-- 仓库选择 -->
        <van-field
          v-if="config.showDepotSelector"
          v-model="depotDisplayName"
          label="仓库"
          placeholder="请选择仓库"
          readonly
          :border="false"
          @click="showDepotSelector"
        />
        
        <!-- 账户选择 -->
        <van-field
          v-if="config.showAccountSelector"
          v-model="accountDisplayName"
          label="结算账户"
          placeholder="请选择账户"
          readonly
          :border="false"
          @click="showAccountSelector"
        />
        
        <!-- 关联单据 -->
        <van-field
          v-if="config.showLinkDocument"
          v-model="formData.linkNumber"
          label="关联单据"
          placeholder="请选择关联单据"
          readonly
          :border="false"
          @click="showLinkDocumentSelector"
        />
      </van-cell-group>
    </div>

    <!-- 商品明细列表 -->
    <div class="product-section">
      <div class="section-header">
        <h3>商品明细</h3>
        <van-button
          v-if="isEditable"
          type="primary"
          size="small"
          icon="plus"
          @click="showProductSelector"
        >
          添加商品
        </van-button>
      </div>
      
      <div class="product-list">
        <div
          v-for="(item, index) in formData.details"
          :key="index"
          class="product-item"
        >
          <div class="product-info">
            <div class="product-name">{{ item.productName }}</div>
            <div class="product-code">{{ item.productCode }}</div>
          </div>
          
          <div class="product-details">
            <div class="detail-row">
              <span class="label">数量:</span>
              <van-stepper
                v-if="isEditable"
                v-model="item.basicNumber"
                :min="0.01"
                :step="1"
                :decimal-length="2"
                @change="updateDetailItem(index, { basicNumber: item.basicNumber })"
              />
              <span v-else class="value">{{ item.basicNumber }}</span>
              <span class="unit">{{ item.productUnit }}</span>
            </div>
            
            <div class="detail-row">
              <span class="label">单价:</span>
              <van-field
                v-if="isEditable"
                v-model="item.unitPrice"
                type="number"
                :border="false"
                class="price-input"
                @blur="updateDetailItem(index, { unitPrice: Number(item.unitPrice) })"
              />
              <span v-else class="value">¥{{ formatCurrency(item.unitPrice) }}</span>
            </div>
            
            <div class="detail-row">
              <span class="label">金额:</span>
              <span class="amount">¥{{ formatCurrency(item.allPrice) }}</span>
            </div>
          </div>
          
          <div v-if="isEditable" class="product-actions">
            <van-button
              type="danger"
              size="small"
              icon="delete"
              @click="removeProduct(index)"
            />
          </div>
        </div>
        
        <van-empty
          v-if="formData.details.length === 0"
          description="暂无商品"
          image="search"
        />
      </div>
    </div>

    <!-- 金额汇总 -->
    <div class="amount-summary">
      <van-cell-group inset>
        <van-cell title="商品数量" :value="detailCount + ' 项'" />
        <van-cell title="合计金额" :value="formattedTotalAmount" />
      </van-cell-group>
    </div>

    <!-- 备注信息 -->
    <div class="remark-section">
      <van-cell-group inset>
        <van-field
          v-model="formData.remark"
          label="备注"
          type="textarea"
          placeholder="请输入备注信息"
          :border="false"
          :readonly="!isEditable"
          rows="3"
          autosize
        />
      </van-cell-group>
    </div>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <van-button
        v-if="isEditable"
        type="default"
        size="large"
        :loading="formState.saving"
        @click="handleSave"
      >
        保存
      </van-button>
      
      <van-button
        v-if="isEditable"
        type="primary"
        size="large"
        :loading="formState.submitting"
        @click="handleSubmit"
      >
        提交
      </van-button>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-datetime-picker
        v-model="selectedDate"
        type="datetime"
        title="选择时间"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 商品选择器 -->
    <ProductSelector
      v-model:show="showProductSelectorModal"
      @select="onProductSelect"
    />

    <!-- 往来单位选择器 -->
    <PartnerSelector
      v-model:show="showPartnerSelectorModal"
      :type="config.partnerType"
      @select="onPartnerSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, defineExpose } from 'vue'
import { showToast } from 'vant'
import { useBusinessForm } from '@/composables/business/useBusinessForm'
import ProductSelector from './ProductSelector.vue'
import PartnerSelector from './PartnerSelector.vue'
import type { FormConfig, BusinessFormData, BaseDetailItem, Product, BasePartner } from '@/types/business'

// Props
interface Props {
  config: FormConfig
  initialData?: Partial<BusinessFormData>
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({})
})

// Emits
const emit = defineEmits<{
  save: [data: BusinessFormData]
  submit: [data: BusinessFormData]
  change: [data: BusinessFormData]
}>()

// 使用业务表单逻辑
const {
  formData,
  formState,
  validationErrors,
  isEditable,
  hasUnsavedChanges,
  totalAmount,
  detailCount,
  initializeForm,
  addDetailItem,
  removeDetailItem,
  updateDetailItem,
  validateForm,
  saveForm,
  submitForm,
  resetForm
} = useBusinessForm(props.config)

// 本地状态
const showDatePicker = ref(false)
const showProductSelectorModal = ref(false)
const showPartnerSelectorModal = ref(false)
const selectedDate = ref(new Date())

// 计算属性
const partnerLabel = computed(() => {
  return props.config.partnerType === 'customer' ? '客户' : '供应商'
})

const partnerDisplayName = computed(() => {
  return formData.partnerName || ''
})

const depotDisplayName = computed(() => {
  return formData.depotName || ''
})

const accountDisplayName = computed(() => {
  return formData.accountName || ''
})

const formattedTotalAmount = computed(() => {
  return `¥${formatCurrency(totalAmount.value)}`
})

// 方法
const formatCurrency = (amount: number | undefined): string => {
  if (!amount) return '0.00'
  return amount.toFixed(2)
}

const onDateConfirm = (value: Date) => {
  formData.operTime = value.toISOString().slice(0, 16)
  showDatePicker.value = false
}

const showProductSelector = () => {
  showProductSelectorModal.value = true
}

const onProductSelect = (product: Product) => {
  addDetailItem({
    productId: product.id,
    productName: product.name,
    productCode: product.code,
    productUnit: product.unitName,
    productBarCode: product.barCode,
    basicNumber: 1,
    unitPrice: product.wholesalePrice || 0,
    allPrice: product.wholesalePrice || 0
  })
  showProductSelectorModal.value = false
}

const showPartnerSelector = () => {
  showPartnerSelectorModal.value = true
}

const onPartnerSelect = (partner: BasePartner) => {
  formData.partnerId = partner.id
  formData.partnerName = partner.name
  showPartnerSelectorModal.value = false
}

const showDepotSelector = () => {
  // TODO: 实现仓库选择器
  showToast('仓库选择功能开发中')
}

const showAccountSelector = () => {
  // TODO: 实现账户选择器
  showToast('账户选择功能开发中')
}

const showLinkDocumentSelector = () => {
  // TODO: 实现关联单据选择器
  showToast('关联单据选择功能开发中')
}

const removeProduct = (index: number) => {
  removeDetailItem(index)
}

const handleSave = async () => {
  const result = await saveForm()
  if (result.success) {
    emit('save', formData)
  }
}

const handleSubmit = async () => {
  const result = await submitForm()
  if (result.success) {
    emit('submit', formData)
  }
}

// 初始化表单
initializeForm(props.initialData)

// 暴露方法给父组件
defineExpose({
  formData,
  validateForm,
  saveForm,
  submitForm,
  resetForm,
  hasUnsavedChanges
})
</script>

<style lang="less" scoped>
.base-business-form {
  padding: 16px;
  background: #f7f8fa;
  min-height: 100vh;

  .form-header {
    margin-bottom: 16px;
  }

  .product-section {
    margin-bottom: 16px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0 12px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .product-list {
      .product-item {
        background: white;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .product-info {
          margin-bottom: 12px;

          .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .product-code {
            font-size: 14px;
            color: #666;
          }
        }

        .product-details {
          .detail-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .label {
              width: 60px;
              font-size: 14px;
              color: #666;
            }

            .value {
              flex: 1;
              font-size: 14px;
              color: #333;
            }

            .unit {
              margin-left: 8px;
              font-size: 14px;
              color: #666;
            }

            .amount {
              flex: 1;
              font-size: 16px;
              font-weight: 600;
              color: #ff6b35;
            }

            .price-input {
              flex: 1;
              padding: 0;
              
              :deep(.van-field__control) {
                text-align: left;
                padding: 0;
              }
            }
          }
        }

        .product-actions {
          display: flex;
          justify-content: flex-end;
          margin-top: 12px;
        }
      }
    }
  }

  .amount-summary {
    margin-bottom: 16px;
  }

  .remark-section {
    margin-bottom: 24px;
  }

  .form-actions {
    display: flex;
    gap: 12px;
    padding: 16px 0;

    .van-button {
      flex: 1;
    }
  }
}
</style>
