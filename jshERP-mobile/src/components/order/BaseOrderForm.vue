<!--
  基础订单表单组件
  
  提供订单表单的基础UI结构和交互逻辑
  可被新增和编辑页面复用
-->
<template>
  <div class="base-order-form">
    <!-- 基础信息 -->
    <van-cell-group title="基础信息" inset>
      <van-field
        v-model="orderForm.customerName"
        label="客户"
        placeholder="请选择客户"
        readonly
        required
        :error="!orderForm.customerName && showValidation"
        error-message="请选择客户"
        @click="showCustomerPicker = true"
      >
        <template #right-icon>
          <van-icon name="arrow" />
        </template>
      </van-field>
      
      <van-field
        v-model="orderForm.orderDate"
        label="单据日期"
        readonly
        required
        :error="!orderForm.orderDate && showValidation"
        error-message="请选择订单日期"
        @click="showDatePicker = true"
      >
        <template #right-icon>
          <van-icon name="arrow" />
        </template>
      </van-field>
      
      <van-field
        v-model="orderForm.orderNo"
        label="单据编号"
        readonly
      />
      
      <van-field
        v-model="orderForm.salesperson"
        label="销售人员"
        placeholder="请选择销售人员"
        readonly
        required
        :error="!orderForm.salesperson && showValidation"
        error-message="请选择销售人员"
        @click="showSalespersonPicker = true"
      >
        <template #right-icon>
          <van-icon name="arrow" />
        </template>
      </van-field>
    </van-cell-group>

    <!-- 商品清单 -->
    <van-cell-group title="商品清单" inset>
      <div class="product-actions">
        <van-button
          type="primary"
          size="small"
          icon="scan"
          @click="handleScanProduct"
        >
          扫描条码
        </van-button>
        <van-button
          type="default"
          size="small"
          icon="plus"
          @click="handleSelectProduct"
        >
          选择商品
        </van-button>
      </div>
      
      <!-- 商品列表为空时的提示 -->
      <div v-if="orderForm.products.length === 0" class="empty-products">
        <van-empty description="暂无商品">
          <template #image>
            <van-icon name="shopping-cart-o" size="60" color="#ddd" />
          </template>
          <div class="empty-tip">
            <p v-if="showValidation" class="error-text">请至少添加一个商品</p>
            <p class="tip-text">点击上方按钮添加商品</p>
          </div>
        </van-empty>
      </div>
      
      <!-- 商品列表 -->
      <div v-else class="product-list">
        <div
          v-for="(product, index) in orderForm.products"
          :key="product.id || index"
          class="product-item"
        >
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-spec">{{ product.spec }}</div>
            <div class="product-unit">单价: ¥{{ formatCurrency(product.price) }}</div>
          </div>
          <div class="product-quantity">
            <van-stepper
              v-model="product.quantity"
              min="1"
              :max="product.stock || 9999"
              @change="handleQuantityChange"
            />
          </div>
          <div class="product-amount">
            <div class="amount-value">¥{{ formatCurrency(product.price * product.quantity) }}</div>
          </div>
          <van-icon
            name="delete-o"
            class="delete-icon"
            @click="removeProduct(index)"
          />
        </div>
        
        <!-- 商品统计 -->
        <div class="product-summary">
          <div class="summary-item">
            <span class="label">商品种类:</span>
            <span class="value">{{ orderForm.products.length }}种</span>
          </div>
          <div class="summary-item">
            <span class="label">商品总数:</span>
            <span class="value">{{ totalQuantity }}件</span>
          </div>
        </div>
      </div>
      
      <!-- 金额汇总 -->
      <div class="amount-summary">
        <van-field
          v-model="formattedSubtotal"
          label="合税合计"
          readonly
        />
        <van-field
          v-model="orderForm.discountRate"
          label="优惠率(%)"
          type="number"
          :formatter="formatDiscountRate"
          @input="handleDiscountChange"
        />
        <van-field
          v-model="formattedDiscountAmount"
          label="收款优惠"
          readonly
        />
        <van-field
          v-model="formattedFinalAmount"
          label="优惠后金额"
          readonly
          class="final-amount"
        />
      </div>
    </van-cell-group>

    <!-- 结算信息 -->
    <van-cell-group title="结算信息" inset>
      <van-field
        v-model="orderForm.paymentAccount"
        label="结算账户"
        placeholder="请选择结算账户"
        readonly
      />
      <van-field
        v-model="orderForm.receivedAmount"
        label="收取订金"
        type="number"
        placeholder="0"
        :formatter="formatAmount"
      />
    </van-cell-group>

    <!-- 备注 -->
    <van-cell-group title="备注" inset>
      <van-field
        v-model="orderForm.remark"
        type="textarea"
        placeholder="请输入备注信息（可选）"
        rows="3"
        maxlength="200"
        show-word-limit
      />
    </van-cell-group>

    <!-- 附件信息 -->
    <van-cell-group title="附件信息" inset>
      <div class="attachment-info">
        <div class="attachment-tip">
          <van-icon name="info-o" />
          图片最多4张，单张大小不超过1M
        </div>
        <van-uploader
          v-model="orderForm.attachments"
          multiple
          :max-count="4"
          :max-size="1024 * 1024"
          upload-icon="plus"
          upload-text="上传图片"
          @oversize="handleOversizeFile"
        />
        <div class="attachment-note">
          <van-icon name="warning-o" />
          提醒：上传word等非图片文件请到电脑端操作
        </div>
      </div>
    </van-cell-group>

    <!-- 日期选择器 -->
    <van-date-picker
      v-model:show="showDatePicker"
      v-model="dateValue"
      title="选择日期"
      @confirm="handleDateConfirm"
    />

    <!-- 客户选择器 -->
    <van-popup v-model:show="showCustomerPicker" position="bottom">
      <div class="picker-header">
        <van-button type="default" @click="showCustomerPicker = false">取消</van-button>
        <span class="picker-title">选择客户</span>
        <van-button type="primary" @click="showCustomerPicker = false">确定</van-button>
      </div>
      <div class="picker-content">
        <van-empty description="客户选择功能开发中" />
      </div>
    </van-popup>

    <!-- 销售人员选择器 -->
    <van-popup v-model:show="showSalespersonPicker" position="bottom">
      <div class="picker-header">
        <van-button type="default" @click="showSalespersonPicker = false">取消</van-button>
        <span class="picker-title">选择销售人员</span>
        <van-button type="primary" @click="showSalespersonPicker = false">确定</van-button>
      </div>
      <div class="picker-content">
        <van-empty description="销售人员选择功能开发中" />
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { showToast } from 'vant'
import { useOrderForm } from '@/composables/useOrderForm'
import type { OrderForm } from '@/types/order'

interface Props {
  /** 初始数据 */
  initialData?: Partial<OrderForm>
  /** 是否显示验证错误 */
  showValidation?: boolean
}

interface Emits {
  (e: 'scan-product'): void
  (e: 'select-product'): void
}

const props = withDefaults(defineProps<Props>(), {
  showValidation: false
})

const emit = defineEmits<Emits>()

// 使用订单表单Hook
const {
  orderForm,
  showCustomerPicker,
  showDatePicker,
  showSalespersonPicker,
  dateValue,
  totalQuantity,
  formatCurrency,
  handleDateConfirm,
  removeProduct,
  handleQuantityChange,
  handleDiscountChange,
  handleOversizeFile,
  initializeForm
} = useOrderForm(props.initialData)

// 格式化显示的金额
const formattedSubtotal = computed(() => `¥${formatCurrency(orderForm.subtotal)}`)
const formattedDiscountAmount = computed(() => `¥${formatCurrency(orderForm.discountAmount)}`)
const formattedFinalAmount = computed(() => `¥${formatCurrency(orderForm.finalAmount)}`)

/**
 * 格式化优惠率
 */
const formatDiscountRate = (value: string): string => {
  const num = parseFloat(value)
  if (isNaN(num)) return '0'
  if (num < 0) return '0'
  if (num > 100) return '100'
  return num.toString()
}

/**
 * 格式化金额输入
 */
const formatAmount = (value: string): string => {
  const num = parseFloat(value)
  if (isNaN(num) || num < 0) return '0'
  return num.toString()
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  emit('scan-product')
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  emit('select-product')
}

// 初始化表单
initializeForm(props.initialData)

// 暴露给父组件的方法和数据
defineExpose({
  orderForm: toRefs(orderForm),
  validateForm: () => {
    // 这里可以添加表单验证逻辑
    return true
  }
})
</script>

<style lang="less" scoped>
.base-order-form {
  padding-bottom: 20px;
}

.product-actions {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 8px 16px;
}

.empty-products {
  padding: 40px 20px;
  
  .empty-tip {
    margin-top: 16px;
    
    .error-text {
      color: #ee0a24;
      font-size: 14px;
      margin-bottom: 8px;
    }
    
    .tip-text {
      color: #969799;
      font-size: 12px;
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .product-info {
    flex: 1;
    
    .product-name {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
      color: #323233;
    }
    
    .product-spec {
      font-size: 12px;
      color: #969799;
      margin-bottom: 2px;
    }
    
    .product-unit {
      font-size: 12px;
      color: #ff6b35;
    }
  }
  
  .product-quantity {
    margin: 0 16px;
  }
  
  .product-amount {
    margin-right: 16px;
    
    .amount-value {
      font-weight: 600;
      color: #ff6b35;
      font-size: 16px;
    }
  }
  
  .delete-icon {
    color: #ee0a24;
    font-size: 18px;
    cursor: pointer;
    
    &:hover {
      opacity: 0.7;
    }
  }
}

.product-summary {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #f0f0f0;
  
  .summary-item {
    .label {
      color: #969799;
      font-size: 12px;
    }
    
    .value {
      color: #323233;
      font-weight: 500;
      margin-left: 4px;
    }
  }
}

.amount-summary {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
  
  :deep(.final-amount) {
    .van-field__label {
      font-weight: 600;
      color: #323233;
    }
    
    .van-field__control {
      font-weight: 600;
      color: #ff6b35;
      font-size: 16px;
    }
  }
}

.attachment-info {
  padding: 12px 16px;
  
  .attachment-tip {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #1989fa;
    margin-bottom: 12px;
    
    .van-icon {
      margin-right: 4px;
    }
  }
  
  .attachment-note {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #ff976a;
    margin-top: 12px;
    
    .van-icon {
      margin-right: 4px;
    }
  }
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  
  .picker-title {
    font-weight: 500;
    font-size: 16px;
  }
}

.picker-content {
  padding: 20px;
  min-height: 200px;
}
</style>
