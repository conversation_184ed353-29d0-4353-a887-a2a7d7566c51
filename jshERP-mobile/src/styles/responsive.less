// ERP移动端响应式设计系统
@import './variables.less';

// 全局响应式基础类
.responsive-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

// 响应式文字大小
.responsive-text {
  // 超小屏幕
  @media (max-width: @erp-mobile-xs) {
    &--xs { font-size: 10px; }
    &--sm { font-size: 11px; }
    &--md { font-size: 12px; }
    &--lg { font-size: 14px; }
    &--xl { font-size: 16px; }
    &--title { font-size: 18px; }
  }
  
  // 小屏幕
  @media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-md) {
    &--xs { font-size: 11px; }
    &--sm { font-size: 12px; }
    &--md { font-size: 14px; }
    &--lg { font-size: 16px; }
    &--xl { font-size: 18px; }
    &--title { font-size: 20px; }
  }
  
  // 中等屏幕
  @media (min-width: @erp-mobile-lg) and (max-width: @erp-tablet) {
    &--xs { font-size: 12px; }
    &--sm { font-size: 13px; }
    &--md { font-size: 15px; }
    &--lg { font-size: 17px; }
    &--xl { font-size: 19px; }
    &--title { font-size: 22px; }
  }
  
  // 平板及以上
  @media (min-width: @erp-tablet) {
    &--xs { font-size: var(--erp-font-size-xs); }
    &--sm { font-size: var(--erp-font-size-sm); }
    &--md { font-size: var(--erp-font-size-md); }
    &--lg { font-size: var(--erp-font-size-lg); }
    &--xl { font-size: var(--erp-font-size-xl); }
    &--title { font-size: var(--erp-font-size-title); }
  }
}

// 响应式间距
.responsive-spacing {
  // 超小屏幕
  @media (max-width: @erp-mobile-xs) {
    &--xs { padding: 2px; margin: 2px; }
    &--sm { padding: 4px; margin: 4px; }
    &--md { padding: 8px; margin: 8px; }
    &--lg { padding: 12px; margin: 12px; }
    &--xl { padding: 16px; margin: 16px; }
  }
  
  // 小屏幕
  @media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-md) {
    &--xs { padding: 4px; margin: 4px; }
    &--sm { padding: 6px; margin: 6px; }
    &--md { padding: 12px; margin: 12px; }
    &--lg { padding: 16px; margin: 16px; }
    &--xl { padding: 20px; margin: 20px; }
  }
  
  // 中等屏幕及以上
  @media (min-width: @erp-mobile-lg) {
    &--xs { padding: var(--erp-spacing-xs); margin: var(--erp-spacing-xs); }
    &--sm { padding: var(--erp-spacing-sm); margin: var(--erp-spacing-sm); }
    &--md { padding: var(--erp-spacing-md); margin: var(--erp-spacing-md); }
    &--lg { padding: var(--erp-spacing-lg); margin: var(--erp-spacing-lg); }
    &--xl { padding: var(--erp-spacing-xl); margin: var(--erp-spacing-xl); }
  }
}

// 响应式按钮尺寸
.responsive-button {
  // 超小屏幕
  @media (max-width: @erp-mobile-xs) {
    &--small { 
      height: 32px; 
      padding: 0 12px; 
      font-size: 12px;
      border-radius: 6px;
    }
    &--medium { 
      height: 36px; 
      padding: 0 16px; 
      font-size: 13px;
      border-radius: 8px;
    }
    &--large { 
      height: 40px; 
      padding: 0 20px; 
      font-size: 14px;
      border-radius: 8px;
    }
  }
  
  // 小屏幕
  @media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-md) {
    &--small { 
      height: 36px; 
      padding: 0 14px; 
      font-size: 13px;
      border-radius: 8px;
    }
    &--medium { 
      height: 40px; 
      padding: 0 18px; 
      font-size: 14px;
      border-radius: 8px;
    }
    &--large { 
      height: 44px; 
      padding: 0 22px; 
      font-size: 15px;
      border-radius: 10px;
    }
  }
  
  // 中等屏幕及以上
  @media (min-width: @erp-mobile-lg) {
    &--small { 
      height: 40px; 
      padding: 0 16px; 
      font-size: 14px;
      border-radius: 8px;
    }
    &--medium { 
      height: 44px; 
      padding: 0 20px; 
      font-size: 15px;
      border-radius: 10px;
    }
    &--large { 
      height: 48px; 
      padding: 0 24px; 
      font-size: 16px;
      border-radius: 12px;
    }
  }
}

// 响应式卡片
.responsive-card {
  // 超小屏幕
  @media (max-width: @erp-mobile-xs) {
    margin: 6px;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }
  
  // 小屏幕
  @media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-md) {
    margin: 8px;
    padding: 16px;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  // 中等屏幕
  @media (min-width: @erp-mobile-lg) and (max-width: @erp-tablet) {
    margin: 12px;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  // 平板及以上
  @media (min-width: @erp-tablet) {
    margin: 16px;
    padding: 24px;
    border-radius: var(--erp-radius-lg);
    box-shadow: var(--erp-shadow-card);
  }
}

// 触摸优化
.touch-optimized {
  @media (hover: none) and (pointer: coarse) {
    // 确保触摸目标足够大
    min-height: 44px;
    min-width: 44px;
    
    // 优化触摸反馈
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    
    // 触摸按钮
    &.button {
      padding: 12px 16px;
      border-radius: 8px;
      
      &:active {
        transform: scale(0.98);
        opacity: 0.8;
      }
    }
    
    // 触摸输入框
    &.input {
      padding: 12px 16px;
      font-size: 16px; // 防止iOS缩放
    }
  }
}

// 横屏优化
@media (orientation: landscape) {
  .landscape-optimized {
    // 横屏时减少垂直间距
    &.container {
      padding-top: 8px;
      padding-bottom: 8px;
    }
    
    // 横屏时调整字体大小
    &.text {
      line-height: 1.3;
    }
    
    // 横屏时优化按钮布局
    &.button-group {
      flex-direction: row;
      gap: 8px;
      
      .button {
        flex: 1;
        max-width: 200px;
      }
    }
  }
  
  // 小屏幕横屏特殊处理
  @media (max-height: 500px) {
    .landscape-compact {
      padding: 4px 8px;
      font-size: 13px;
      
      .header {
        padding: 8px 16px;
        font-size: 14px;
      }
      
      .content {
        padding: 8px 16px;
      }
      
      .footer {
        padding: 8px 16px;
      }
    }
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-optimized {
    // 优化字体渲染
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    // 优化图标渲染
    .icon {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
    
    // 优化边框
    .border {
      border-width: 0.5px;
    }
  }
}

// 无障碍优化
@media (prefers-reduced-motion: reduce) {
  .accessibility-optimized {
    // 减少动画
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@media (prefers-contrast: high) {
  .accessibility-optimized {
    // 高对比度
    .text {
      color: #000000;
      background: #ffffff;
    }
    
    .button {
      border: 2px solid #000000;
    }
  }
}

// 响应式网格系统
.responsive-grid {
  display: grid;
  gap: var(--erp-spacing-md);
  
  // 超小屏幕：单列
  @media (max-width: @erp-mobile-xs) {
    grid-template-columns: 1fr;
    gap: var(--erp-spacing-sm);
  }
  
  // 小屏幕：双列
  @media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-md) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  // 中等屏幕：三列
  @media (min-width: @erp-mobile-lg) and (max-width: @erp-tablet) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  // 平板及以上：四列
  @media (min-width: @erp-tablet) {
    grid-template-columns: repeat(4, 1fr);
  }
}
