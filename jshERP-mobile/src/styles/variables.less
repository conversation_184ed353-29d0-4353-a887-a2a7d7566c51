// ERP移动端设计系统变量
:root {
  // ERP主题色彩
  --erp-primary: #4A90E2;
  --erp-primary-dark: #357ABD;
  --erp-primary-light: #6BA3E8;
  --erp-gradient: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  
  // ERP功能色彩
  --erp-orange: #FF6B35;
  --erp-orange-light: #FF8C42;
  --erp-orange-dark: #E55A2B;
  --erp-success: #52C41A;
  --erp-warning: #FAAD14;
  --erp-error: #FF4D4F;
  --erp-info: #1890FF;
  
  // ERP背景色
  --erp-bg-primary: #FFFFFF;
  --erp-bg-secondary: #F5F5F5;
  --erp-bg-tertiary: #FAFAFA;
  --erp-bg-card: #FFFFFF;
  --erp-bg-overlay: rgba(0, 0, 0, 0.5);
  
  // ERP文字色
  --erp-text-primary: #333333;
  --erp-text-secondary: #666666;
  --erp-text-tertiary: #999999;
  --erp-text-quaternary: #CCCCCC;
  --erp-text-white: #FFFFFF;
  --erp-text-inverse: #FFFFFF;
  
  // ERP边框色
  --erp-border-light: #E5E5E5;
  --erp-border-medium: #D9D9D9;
  --erp-border-dark: #BFBFBF;
  --erp-border-primary: #4A90E2;
  
  // ERP阴影
  --erp-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --erp-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --erp-shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
  --erp-shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  // ERP圆角
  --erp-radius-xs: 2px;
  --erp-radius-sm: 4px;
  --erp-radius-md: 8px;
  --erp-radius-lg: 12px;
  --erp-radius-xl: 16px;
  --erp-radius-2xl: 20px;
  --erp-radius-round: 50%;
  
  // ERP间距
  --erp-spacing-xs: 4px;
  --erp-spacing-sm: 8px;
  --erp-spacing-md: 16px;
  --erp-spacing-lg: 24px;
  --erp-spacing-xl: 32px;
  --erp-spacing-2xl: 48px;
  --erp-spacing-3xl: 64px;
  
  // ERP字体大小
  --erp-font-size-xs: 10px;
  --erp-font-size-sm: 12px;
  --erp-font-size-md: 14px;
  --erp-font-size-lg: 16px;
  --erp-font-size-xl: 18px;
  --erp-font-size-2xl: 20px;
  --erp-font-size-3xl: 24px;
  --erp-font-size-4xl: 28px;
  --erp-font-size-title: 24px;
  --erp-font-size-subtitle: 16px;
  
  // ERP行高
  --erp-line-height-tight: 1.2;
  --erp-line-height-normal: 1.5;
  --erp-line-height-loose: 1.8;
  
  // ERP字体粗细
  --erp-font-weight-normal: 400;
  --erp-font-weight-medium: 500;
  --erp-font-weight-semibold: 600;
  --erp-font-weight-bold: 700;
  
  // ERP Z-index层级
  --erp-z-base: 1;
  --erp-z-header: 100;
  --erp-z-tabbar: 200;
  --erp-z-dropdown: 300;
  --erp-z-sticky: 400;
  --erp-z-fixed: 500;
  --erp-z-modal-backdrop: 1000;
  --erp-z-modal: 1010;
  --erp-z-popover: 1020;
  --erp-z-tooltip: 1030;
  --erp-z-toast: 2000;
  
  // ERP动画时间
  --erp-duration-fast: 0.15s;
  --erp-duration-normal: 0.3s;
  --erp-duration-slow: 0.5s;
  --erp-duration-page-transition: 0.4s;

  // ERP缓动函数
  --erp-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --erp-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --erp-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --erp-ease-page-transition: cubic-bezier(0.4, 0, 0.2, 1);

  // ERP页面过渡动画变量
  --erp-transition-slide-distance: 100%;
  --erp-transition-scale-from: 0.9;
  --erp-transition-scale-to: 1.1;
  --erp-transition-fade-opacity: 0;
}

// ERP移动端断点
@erp-mobile-xs: 320px;
@erp-mobile-sm: 375px;
@erp-mobile-md: 414px;
@erp-mobile-lg: 480px;
@erp-tablet: 768px;
@erp-desktop: 1024px;
