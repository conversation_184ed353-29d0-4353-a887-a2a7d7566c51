/**
 * 性能优化工具类
 * 
 * 提供性能监控、优化和测试相关的工具函数
 */

// 性能监控接口
export interface PerformanceMetrics {
  // 页面加载性能
  loadTime: number
  domContentLoaded: number
  firstPaint: number
  firstContentfulPaint: number
  
  // 内存使用
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  
  // 动画性能
  fps: number
  frameDrops: number
  
  // 网络性能
  networkLatency: number
  resourceLoadTime: number
}

// 性能监控类
export class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private frameCount = 0
  private lastFrameTime = 0
  private frameDrops = 0
  
  /**
   * 开始性能监控
   */
  startMonitoring(): void {
    this.monitorPageLoad()
    this.monitorMemoryUsage()
    this.monitorFrameRate()
    this.monitorNetworkPerformance()
  }
  
  /**
   * 监控页面加载性能
   */
  private monitorPageLoad(): void {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      if (navigation) {
        this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart
        this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
      }
      
      // 监控First Paint和First Contentful Paint
      const paintEntries = performance.getEntriesByType('paint')
      paintEntries.forEach(entry => {
        if (entry.name === 'first-paint') {
          this.metrics.firstPaint = entry.startTime
        } else if (entry.name === 'first-contentful-paint') {
          this.metrics.firstContentfulPaint = entry.startTime
        }
      })
    }
  }
  
  /**
   * 监控内存使用
   */
  private monitorMemoryUsage(): void {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory
      this.metrics.usedJSHeapSize = memory.usedJSHeapSize
      this.metrics.totalJSHeapSize = memory.totalJSHeapSize
      this.metrics.jsHeapSizeLimit = memory.jsHeapSizeLimit
    }
  }
  
  /**
   * 监控帧率
   */
  private monitorFrameRate(): void {
    const measureFPS = (timestamp: number) => {
      if (this.lastFrameTime) {
        const delta = timestamp - this.lastFrameTime
        const fps = 1000 / delta
        
        // 检测掉帧（低于55fps认为是掉帧）
        if (fps < 55) {
          this.frameDrops++
        }
        
        this.frameCount++
        
        // 每秒计算一次平均FPS
        if (this.frameCount >= 60) {
          this.metrics.fps = fps
          this.metrics.frameDrops = this.frameDrops
          this.frameCount = 0
          this.frameDrops = 0
        }
      }
      
      this.lastFrameTime = timestamp
      requestAnimationFrame(measureFPS)
    }
    
    requestAnimationFrame(measureFPS)
  }
  
  /**
   * 监控网络性能
   */
  private monitorNetworkPerformance(): void {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const resources = performance.getEntriesByType('resource')
      let totalLoadTime = 0
      let resourceCount = 0
      
      resources.forEach(resource => {
        const loadTime = resource.responseEnd - resource.requestStart
        totalLoadTime += loadTime
        resourceCount++
      })
      
      if (resourceCount > 0) {
        this.metrics.resourceLoadTime = totalLoadTime / resourceCount
      }
      
      // 估算网络延迟
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        this.metrics.networkLatency = navigation.responseStart - navigation.requestStart
      }
    }
  }
  
  /**
   * 获取性能指标
   */
  getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }
  
  /**
   * 重置监控数据
   */
  reset(): void {
    this.metrics = {}
    this.frameCount = 0
    this.lastFrameTime = 0
    this.frameDrops = 0
  }
}

// 性能优化工具函数

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 图片懒加载
 */
export function lazyLoadImage(img: HTMLImageElement, src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const image = entry.target as HTMLImageElement
          image.src = src
          image.onload = () => {
            observer.unobserve(image)
            resolve()
          }
          image.onerror = () => {
            observer.unobserve(image)
            reject(new Error('Image load failed'))
          }
        }
      })
    })
    
    observer.observe(img)
  })
}

/**
 * 组件懒加载
 */
export function lazyLoadComponent(importFunc: () => Promise<any>) {
  return () => ({
    component: importFunc(),
    loading: {
      template: '<div class="loading">加载中...</div>'
    },
    error: {
      template: '<div class="error">加载失败</div>'
    },
    delay: 200,
    timeout: 10000
  })
}

/**
 * 内存泄漏检测
 */
export class MemoryLeakDetector {
  private initialMemory: number = 0
  private checkInterval: NodeJS.Timeout | null = null
  
  start(): void {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      this.initialMemory = (performance as any).memory.usedJSHeapSize
      
      this.checkInterval = setInterval(() => {
        const currentMemory = (performance as any).memory.usedJSHeapSize
        const memoryIncrease = currentMemory - this.initialMemory
        
        // 如果内存增长超过50MB，发出警告
        if (memoryIncrease > 50 * 1024 * 1024) {
          console.warn('Potential memory leak detected:', {
            initial: this.formatBytes(this.initialMemory),
            current: this.formatBytes(currentMemory),
            increase: this.formatBytes(memoryIncrease)
          })
        }
      }, 30000) // 每30秒检查一次
    }
  }
  
  stop(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }
  
  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }
}

/**
 * 动画性能优化
 */
export function optimizeAnimation(element: HTMLElement): void {
  // 启用硬件加速
  element.style.transform = 'translateZ(0)'
  element.style.willChange = 'transform, opacity'
  
  // 优化重绘
  element.style.backfaceVisibility = 'hidden'
  element.style.perspective = '1000px'
}

/**
 * 移除动画优化
 */
export function removeAnimationOptimization(element: HTMLElement): void {
  element.style.transform = ''
  element.style.willChange = 'auto'
  element.style.backfaceVisibility = ''
  element.style.perspective = ''
}

/**
 * 批量DOM操作优化
 */
export function batchDOMUpdates(updates: () => void): void {
  requestAnimationFrame(() => {
    updates()
  })
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()
export const memoryLeakDetector = new MemoryLeakDetector()

// 自动启动性能监控（仅在浏览器环境）
if (typeof window !== 'undefined') {
  performanceMonitor.startMonitoring()
  memoryLeakDetector.start()
}
