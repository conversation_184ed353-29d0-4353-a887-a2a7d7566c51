<!--
  编辑销售订单页面

  功能：
  - 加载现有订单数据
  - 编辑订单信息
  - 更新订单
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header">
      <van-nav-bar
        title="编辑订单"
        left-arrow
        @click-left="handleBack"
      >
        <template #right>
          <van-icon name="question-o" @click="showHelp" />
        </template>
      </van-nav-bar>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" size="24px">加载订单数据...</van-loading>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="loadError" class="error-container">
      <van-empty description="加载失败">
        <template #image>
          <van-icon name="warning-o" size="60" color="#ee0a24" />
        </template>
        <div class="error-actions">
          <van-button type="primary" @click="loadOrderDetail">重新加载</van-button>
          <van-button type="default" @click="$router.back()">返回</van-button>
        </div>
      </van-empty>
    </div>

    <!-- 订单表单 -->
    <div v-else class="order-form">
      <!-- 订单状态信息 -->
      <van-cell-group v-if="orderForm.status" title="订单状态" inset>
        <van-field label="当前状态" readonly>
          <template #input>
            <van-tag :type="getStatusType(orderForm.status)">
              {{ getStatusText(orderForm.status) }}
            </van-tag>
          </template>
        </van-field>
        <van-field
          v-if="orderForm.createTime"
          v-model="orderForm.createTime"
          label="创建时间"
          readonly
        />
        <van-field
          v-if="orderForm.updateTime"
          v-model="orderForm.updateTime"
          label="更新时间"
          readonly
        />
      </van-cell-group>

      <BaseOrderForm
        ref="orderFormRef"
        :initial-data="orderForm"
        :show-validation="showValidation"
        @scan-product="handleScanProduct"
        @select-product="handleSelectProduct"
      />
    </div>


    <!-- 底部操作栏 -->
    <div v-if="!loading && !loadError" class="action-bar">
      <van-button
        type="success"
        size="large"
        :loading="submitting"
        :disabled="!isFormValid || !hasChanges"
        @click="handleUpdate"
      >
        更新订单
      </van-button>
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        @click="handleSave"
      >
        保存草稿
      </van-button>
    </div>

    <!-- 帮助弹窗 -->
    <van-popup v-model:show="showHelpDialog" position="center" class="help-popup">
      <div class="help-content">
        <h3>编辑说明</h3>
        <div class="help-item">
          <h4>1. 订单状态</h4>
          <p>显示当前订单的状态和时间信息</p>
        </div>
        <div class="help-item">
          <h4>2. 编辑限制</h4>
          <p>已提交的订单部分信息可能无法修改</p>
        </div>
        <div class="help-item">
          <h4>3. 保存选项</h4>
          <p>• 保存草稿：保存当前修改，不改变订单状态</p>
          <p>• 更新订单：提交修改并更新订单</p>
        </div>
        <van-button type="primary" block @click="showHelpDialog = false">
          我知道了
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import BaseOrderForm from '@/components/order/BaseOrderForm.vue'
import { useOrderForm } from '@/composables/useOrderForm'
import type { OrderStatus } from '@/types/order'

const router = useRouter()
const route = useRoute()

// 获取订单ID
const orderId = computed(() => route.params.id as string)

// 使用订单表单Hook
const {
  orderForm,
  loading: formLoading,
  submitting,
  isFormValid,
  validateForm,
  initializeForm
} = useOrderForm()

// 页面状态
const loading = ref<boolean>(true)
const loadError = ref<boolean>(false)
const showValidation = ref<boolean>(false)
const showHelpDialog = ref<boolean>(false)
const orderFormRef = ref()
const originalData = ref<any>(null)

// 计算是否有变更
const hasChanges = computed(() => {
  if (!originalData.value) return false

  // 简单的深度比较（实际项目中可使用lodash.isEqual）
  return JSON.stringify(orderForm) !== JSON.stringify(originalData.value)
})

/**
 * 获取订单状态类型
 */
const getStatusType = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.DRAFT]: 'default',
    [OrderStatus.PENDING]: 'warning',
    [OrderStatus.APPROVED]: 'success',
    [OrderStatus.SHIPPED]: 'primary',
    [OrderStatus.COMPLETED]: 'success',
    [OrderStatus.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'default'
}

/**
 * 获取订单状态文本
 */
const getStatusText = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.DRAFT]: '草稿',
    [OrderStatus.PENDING]: '待审核',
    [OrderStatus.APPROVED]: '已审核',
    [OrderStatus.SHIPPED]: '已发货',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 返回处理
 */
const handleBack = async (): Promise<void> => {
  if (hasChanges.value) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的修改，确定要离开吗？'
      })
    } catch {
      return // 用户取消
    }
  }
  router.back()
}

/**
 * 显示帮助
 */
const showHelp = (): void => {
  showHelpDialog.value = true
}

/**
 * 加载订单详情
 */
const loadOrderDetail = async (id?: string): Promise<void> => {
  const targetId = id || orderId.value

  if (!targetId) {
    loadError.value = true
    loading.value = false
    showToast({ type: 'fail', message: '订单ID不能为空' })
    return
  }

  try {
    loading.value = true
    loadError.value = false

    // TODO: 调用API加载订单详情
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    const mockOrder = {
      orderNo: `XSDD000013249${targetId}`,
      customerName: '测试客户',
      customerId: 'CUST001',
      orderDate: '2025-06-24 00:02:10',
      salesperson: 'waterxiggg',
      salespersonId: 'SALES001',
      products: [
        {
          id: '1001',
          name: '珐琅工艺品',
          spec: '规格1',
          price: 120,
          quantity: 1
        },
        {
          id: '1002',
          name: '景泰蓝花瓶',
          spec: '中号',
          price: 280,
          quantity: 2
        }
      ],
      subtotal: 680,
      discountRate: 10,
      discountAmount: 68,
      finalAmount: 612,
      paymentAccount: '测试账户',
      receivedAmount: 100,
      remark: '客户要求加急处理',
      attachments: [],
      status: OrderStatus.PENDING,
      createTime: '2025-06-24 00:02:10',
      updateTime: '2025-06-24 10:30:15'
    }

    // 更新表单数据
    Object.assign(orderForm, mockOrder)

    // 保存原始数据用于比较
    originalData.value = JSON.parse(JSON.stringify(mockOrder))

    showToast({ type: 'success', message: '订单加载成功' })
  } catch (error) {
    loadError.value = true
    showToast({ type: 'fail', message: '加载订单失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  showToast('扫描功能开发中')
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  showToast('选择商品功能开发中')
}

/**
 * 保存草稿
 */
const handleSave = async (): Promise<void> => {
  try {
    // 验证表单
    if (orderFormRef.value) {
      const isValid = await orderFormRef.value.validateForm()
      if (!isValid) {
        showValidation.value = true
        showToast({ type: 'fail', message: '请检查表单信息' })
        return
      }
    }

    submitting.value = true

    // TODO: 调用API保存草稿
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新原始数据
    originalData.value = JSON.parse(JSON.stringify(orderForm))

    showToast({ type: 'success', message: '保存成功' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    submitting.value = false
  }
}

/**
 * 更新订单
 */
const handleUpdate = async (): Promise<void> => {
  try {
    // 验证表单
    if (orderFormRef.value) {
      const isValid = await orderFormRef.value.validateForm()
      if (!isValid) {
        showValidation.value = true
        showToast({ type: 'fail', message: '请检查表单信息' })
        return
      }
    }

    // 确认更新
    try {
      await showConfirmDialog({
        title: '确认更新',
        message: '确定要更新这个订单吗？'
      })
    } catch {
      return // 用户取消
    }

    submitting.value = true

    // TODO: 调用API更新订单
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新原始数据
    originalData.value = JSON.parse(JSON.stringify(orderForm))

    showToast({ type: 'success', message: '订单更新成功' })

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      router.back()
    }, 1000)
  } catch (error) {
    showToast({ type: 'fail', message: '更新失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 监听路由变化，重新加载数据
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadOrderDetail(newId as string)
  }
}, { immediate: false })

// 组件挂载时加载订单详情
onMounted(() => {
  loadOrderDetail()
})

// 页面离开前的确认
onBeforeUnmount(() => {
  // 清理工作
})
</script>

<style lang="less" scoped>
.loading-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.order-form {
  padding-bottom: 80px; // 为底部操作栏留空间
}

.product-actions {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
}

.empty-products {
  padding: 20px;
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .product-info {
    flex: 1;
    
    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .product-spec {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-quantity {
    margin: 0 12px;
  }
  
  .product-price {
    font-weight: 500;
    color: #ff6b35;
    margin-right: 12px;
  }
  
  .van-icon {
    color: #ee0a24;
    cursor: pointer;
  }
}

.amount-summary {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.attachment-info {
  padding: 12px 16px;
  
  .attachment-tip {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .attachment-note {
    font-size: 12px;
    color: #666;
    margin-top: 12px;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  
  .van-button {
    flex: 1;
  }
}
</style>
