<!--
  编辑销售订单页面

  功能：
  - 加载现有订单数据
  - 编辑订单信息
  - 更新订单
-->
<template>
  <div class="sales-order-edit">
    <!-- 页面头部 -->
    <div class="page-header">
      <van-nav-bar
        title="编辑销售订单"
        left-arrow
        @click-left="handleBack"
        class="nav-bar"
      >
        <template #right>
          <div class="header-actions">
            <van-icon
              name="question-o"
              class="help-icon"
              @click="showHelp"
            />
          </div>
        </template>
      </van-nav-bar>

      <!-- 页面状态指示器 -->
      <div class="page-status">
        <div class="status-item completed">
          <div class="status-dot"></div>
          <span class="status-text">加载数据</span>
        </div>
        <div class="status-line"></div>
        <div class="status-item active">
          <div class="status-dot"></div>
          <span class="status-text">编辑信息</span>
        </div>
        <div class="status-line"></div>
        <div class="status-item">
          <div class="status-dot"></div>
          <span class="status-text">保存更新</span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" size="32px" color="#1989fa">
        <span class="loading-text">加载订单数据...</span>
      </van-loading>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="loadError" class="error-container">
      <van-empty description="订单加载失败，请重试">
        <template #image>
          <van-icon name="warning-o" size="80" color="#ee0a24" />
        </template>
        <div class="error-actions">
          <van-button
            type="primary"
            round
            @click="loadOrderDetail"
            class="retry-button"
          >
            <van-icon name="replay" />
            重新加载
          </van-button>
          <van-button
            type="default"
            round
            @click="$router.back()"
            class="back-button"
          >
            <van-icon name="arrow-left" />
            返回
          </van-button>
        </div>
      </van-empty>
    </div>

    <!-- 订单表单容器 -->
    <div v-else class="form-container">
      <!-- 订单状态信息卡片 -->
      <div v-if="orderForm.status" class="status-card">
        <div class="status-header">
          <h3>订单状态</h3>
          <van-tag
            :type="getStatusType(orderForm.status)"
            size="large"
            class="status-tag"
          >
            {{ getStatusText(orderForm.status) }}
          </van-tag>
        </div>

        <div class="status-details">
          <div class="detail-item">
            <span class="label">创建时间</span>
            <span class="value">{{ orderForm.createTime || '2024-01-15 10:30:00' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">最后更新</span>
            <span class="value">{{ orderForm.updateTime || '2024-01-15 14:20:00' }}</span>
          </div>
        </div>
      </div>

      <!-- 订单表单 -->
      <BaseOrderForm
        ref="orderFormRef"
        :initial-data="orderForm"
        :show-validation="showValidation"
        @scan-product="handleScanProduct"
        @select-product="handleSelectProduct"
      />
    </div>

    <!-- 底部操作栏 -->
    <div v-if="!loading && !loadError" class="action-bar">
      <van-button
        type="success"
        size="large"
        :loading="submitting"
        :disabled="!isFormValid || !hasChanges"
        @click="handleUpdate"
        class="action-button primary-action"
      >
        <van-icon name="success" />
        更新订单
      </van-button>
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        @click="handleSave"
        class="action-button secondary-action"
      >
        <van-icon name="edit" />
        保存草稿
      </van-button>
    </div>

    <!-- 帮助弹窗 -->
    <van-popup
      v-model:show="showHelpDialog"
      position="center"
      class="help-popup"
      :close-on-click-overlay="true"
      round
    >
      <div class="help-content">
        <div class="help-header">
          <h3>编辑说明</h3>
          <van-icon
            name="cross"
            class="close-icon"
            @click="showHelpDialog = false"
          />
        </div>

        <div class="help-body">
          <div class="help-item">
            <div class="help-icon">
              <van-icon name="info-o" />
            </div>
            <div class="help-text">
              <h4>1. 订单状态</h4>
              <p>显示当前订单的状态和时间信息</p>
            </div>
          </div>

          <div class="help-item">
            <div class="help-icon">
              <van-icon name="lock" />
            </div>
            <div class="help-text">
              <h4>2. 编辑限制</h4>
              <p>已提交的订单部分信息可能无法修改</p>
            </div>
          </div>

          <div class="help-item">
            <div class="help-icon">
              <van-icon name="completed" />
            </div>
            <div class="help-text">
              <h4>3. 保存选项</h4>
              <p>• 保存草稿：保存当前修改，不改变订单状态</p>
              <p>• 更新订单：提交修改并更新订单</p>
            </div>
          </div>
        </div>

        <div class="help-footer">
          <van-button
            type="primary"
            block
            round
            @click="showHelpDialog = false"
          >
            我知道了
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import BaseOrderForm from '@/components/order/BaseOrderForm.vue'
import { useOrderForm } from '@/composables/useOrderForm'
import type { OrderStatus } from '@/types/order'

const router = useRouter()
const route = useRoute()

// 获取订单ID
const orderId = computed(() => route.params.id as string)

// 使用订单表单Hook
const {
  orderForm,
  loading: formLoading,
  submitting,
  isFormValid,
  validateForm,
  initializeForm
} = useOrderForm()

// 页面状态
const loading = ref<boolean>(true)
const loadError = ref<boolean>(false)
const showValidation = ref<boolean>(false)
const showHelpDialog = ref<boolean>(false)
const orderFormRef = ref()
const originalData = ref<any>(null)

// 计算是否有变更
const hasChanges = computed(() => {
  if (!originalData.value) return false

  // 简单的深度比较（实际项目中可使用lodash.isEqual）
  return JSON.stringify(orderForm) !== JSON.stringify(originalData.value)
})

/**
 * 获取订单状态类型
 */
const getStatusType = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.DRAFT]: 'default',
    [OrderStatus.PENDING]: 'warning',
    [OrderStatus.APPROVED]: 'success',
    [OrderStatus.SHIPPED]: 'primary',
    [OrderStatus.COMPLETED]: 'success',
    [OrderStatus.CANCELLED]: 'danger'
  }
  return statusMap[status] || 'default'
}

/**
 * 获取订单状态文本
 */
const getStatusText = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.DRAFT]: '草稿',
    [OrderStatus.PENDING]: '待审核',
    [OrderStatus.APPROVED]: '已审核',
    [OrderStatus.SHIPPED]: '已发货',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 返回处理
 */
const handleBack = async (): Promise<void> => {
  if (hasChanges.value) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的修改，确定要离开吗？'
      })
    } catch {
      return // 用户取消
    }
  }
  router.back()
}

/**
 * 显示帮助
 */
const showHelp = (): void => {
  showHelpDialog.value = true
}

/**
 * 加载订单详情
 */
const loadOrderDetail = async (id?: string): Promise<void> => {
  const targetId = id || orderId.value

  if (!targetId) {
    loadError.value = true
    loading.value = false
    showToast({ type: 'fail', message: '订单ID不能为空' })
    return
  }

  try {
    loading.value = true
    loadError.value = false

    // TODO: 调用API加载订单详情
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    const mockOrder = {
      orderNo: `XSDD000013249${targetId}`,
      customerName: '测试客户',
      customerId: 'CUST001',
      orderDate: '2025-06-24 00:02:10',
      salesperson: 'waterxiggg',
      salespersonId: 'SALES001',
      products: [
        {
          id: '1001',
          name: '珐琅工艺品',
          spec: '规格1',
          price: 120,
          quantity: 1
        },
        {
          id: '1002',
          name: '景泰蓝花瓶',
          spec: '中号',
          price: 280,
          quantity: 2
        }
      ],
      subtotal: 680,
      discountRate: 10,
      discountAmount: 68,
      finalAmount: 612,
      paymentAccount: '测试账户',
      receivedAmount: 100,
      remark: '客户要求加急处理',
      attachments: [],
      status: OrderStatus.PENDING,
      createTime: '2025-06-24 00:02:10',
      updateTime: '2025-06-24 10:30:15'
    }

    // 更新表单数据
    Object.assign(orderForm, mockOrder)

    // 保存原始数据用于比较
    originalData.value = JSON.parse(JSON.stringify(mockOrder))

    showToast({ type: 'success', message: '订单加载成功' })
  } catch (error) {
    loadError.value = true
    showToast({ type: 'fail', message: '加载订单失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  showToast('扫描功能开发中')
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  showToast('选择商品功能开发中')
}

/**
 * 保存草稿
 */
const handleSave = async (): Promise<void> => {
  try {
    // 验证表单
    if (orderFormRef.value) {
      const isValid = await orderFormRef.value.validateForm()
      if (!isValid) {
        showValidation.value = true
        showToast({ type: 'fail', message: '请检查表单信息' })
        return
      }
    }

    submitting.value = true

    // TODO: 调用API保存草稿
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新原始数据
    originalData.value = JSON.parse(JSON.stringify(orderForm))

    showToast({ type: 'success', message: '保存成功' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    submitting.value = false
  }
}

/**
 * 更新订单
 */
const handleUpdate = async (): Promise<void> => {
  try {
    // 验证表单
    if (orderFormRef.value) {
      const isValid = await orderFormRef.value.validateForm()
      if (!isValid) {
        showValidation.value = true
        showToast({ type: 'fail', message: '请检查表单信息' })
        return
      }
    }

    // 确认更新
    try {
      await showConfirmDialog({
        title: '确认更新',
        message: '确定要更新这个订单吗？'
      })
    } catch {
      return // 用户取消
    }

    submitting.value = true

    // TODO: 调用API更新订单
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 更新原始数据
    originalData.value = JSON.parse(JSON.stringify(orderForm))

    showToast({ type: 'success', message: '订单更新成功' })

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      router.back()
    }, 1000)
  } catch (error) {
    showToast({ type: 'fail', message: '更新失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 监听路由变化，重新加载数据
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadOrderDetail(newId as string)
  }
}, { immediate: false })

// 组件挂载时加载订单详情
onMounted(() => {
  loadOrderDetail()
})

// 页面离开前的确认
onBeforeUnmount(() => {
  // 清理工作
})
</script>

<style lang="less" scoped>
// 动画定义
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.sales-order-edit {
  min-height: 100vh;
  background: linear-gradient(135deg, #f7f8fa 0%, #e9ecef 100%);
  animation: fadeIn 0.3s ease-out;
}

.page-header {
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  animation: slideInDown 0.4s ease-out;

  .nav-bar {
    :deep(.van-nav-bar__title) {
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-nav-bar__left) {
      .van-icon {
        font-size: 20px;
        color: #323233;
        transition: all 0.2s ease;

        &:hover {
          color: #1989fa;
        }
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .help-icon {
      font-size: 20px;
      color: #1989fa;
      cursor: pointer;
      padding: 6px;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:hover {
        background: #e6f7ff;
        transform: scale(1.1);
      }

      &:active {
        animation: buttonPress 0.2s ease;
      }
    }
  }

  .page-status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

    .status-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #dcdee0;
        transition: all 0.3s ease;
      }

      .status-text {
        font-size: 12px;
        color: #969799;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      &.completed {
        .status-dot {
          background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
        }

        .status-text {
          color: #52c41a;
          font-weight: 600;
        }
      }

      &.active {
        .status-dot {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          animation: pulse 2s infinite;
        }

        .status-text {
          color: #323233;
          font-weight: 600;
        }
      }
    }

    .status-line {
      width: 40px;
      height: 2px;
      background: #dcdee0;
      margin: 0 16px;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  animation: fadeIn 0.5s ease-out;

  .van-loading {
    margin-bottom: 16px;
  }

  .loading-text {
    color: #646566;
    font-size: 14px;
    margin-top: 12px;
  }
}

.error-container {
  padding: 60px 20px;
  text-align: center;
  animation: fadeIn 0.5s ease-out;

  .van-empty {
    :deep(.van-empty__image) {
      margin-bottom: 20px;
    }

    :deep(.van-empty__description) {
      font-size: 16px;
      color: #646566;
      margin-bottom: 24px;
    }
  }

  .error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 24px;

    .retry-button, .back-button {
      min-width: 120px;
      height: 44px;
      font-weight: 500;
      transition: all 0.3s ease;

      :deep(.van-icon) {
        margin-right: 6px;
        font-size: 16px;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }

    .retry-button {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;
    }
  }
}

.form-container {
  animation: slideInUp 0.5s ease-out 0.2s both;

  .status-card {
    margin: 16px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    animation: slideInUp 0.4s ease-out 0.3s both;

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px 16px 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: white;
      }

      .status-tag {
        :deep(.van-tag) {
          font-size: 13px;
          font-weight: 600;
          padding: 6px 12px;
          border-radius: 16px;
        }
      }
    }

    .status-details {
      padding: 20px 24px;

      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #646566;
          font-size: 14px;
          font-weight: 500;
        }

        .value {
          color: #323233;
          font-size: 14px;
          font-weight: 600;
        }
      }
    }
  }

  .order-form {
    padding-bottom: 100px; // 为底部操作栏留空间
  }
}

.product-actions {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
}

.empty-products {
  padding: 20px;
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .product-info {
    flex: 1;
    
    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .product-spec {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-quantity {
    margin: 0 12px;
  }
  
  .product-price {
    font-weight: 500;
    color: #ff6b35;
    margin-right: 12px;
  }
  
  .van-icon {
    color: #ee0a24;
    cursor: pointer;
  }
}

.amount-summary {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.attachment-info {
  padding: 12px 16px;
  
  .attachment-tip {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .attachment-note {
    font-size: 12px;
    color: #666;
    margin-top: 12px;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 16px 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 100;
  animation: slideInUp 0.6s ease-out 0.4s both;

  .action-button {
    flex: 1;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    height: 48px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
      animation: buttonPress 0.2s ease;
    }

    &.primary-action {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      border: none;

      :deep(.van-icon) {
        margin-right: 6px;
        font-size: 18px;
      }

      &:hover {
        background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
      }
    }

    &.secondary-action {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;

      :deep(.van-icon) {
        margin-right: 6px;
        font-size: 18px;
      }

      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      }
    }

    &:disabled {
      opacity: 0.6;
      transform: none;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }
}

.help-popup {
  :deep(.van-popup) {
    border-radius: 16px;
    overflow: hidden;
  }

  .help-content {
    padding: 0;
    max-width: 360px;
    background: white;

    .help-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px 16px 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }

      .close-icon {
        font-size: 20px;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }

    .help-body {
      padding: 24px;

      .help-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .help-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          flex-shrink: 0;

          .van-icon {
            font-size: 18px;
            color: #1989fa;
          }
        }

        .help-text {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 15px;
            font-weight: 600;
            color: #323233;
          }

          p {
            margin: 0 0 4px 0;
            font-size: 14px;
            color: #646566;
            line-height: 20px;
          }
        }
      }
    }

    .help-footer {
      padding: 16px 24px 24px 24px;

      .van-button {
        height: 44px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
        }
      }
    }
  }
}

// 响应式设计优化
@media (max-width: @erp-mobile-xs) {
  .sales-order-edit {
    // 小屏幕优化
    .page-header {
      padding: 12px 16px;

      .header-content {
        .page-title {
          font-size: 16px;
        }

        .help-icon {
          width: 32px;
          height: 32px;

          .van-icon {
            font-size: 16px;
          }
        }
      }
    }

    .progress-container {
      padding: 12px 16px;

      .progress-steps {
        .step {
          .step-number {
            width: 24px;
            height: 24px;
            font-size: 12px;
          }

          .step-label {
            font-size: 12px;
          }
        }

        .step-connector {
          height: 2px;
        }
      }
    }

    .loading-container {
      padding: 40px 16px;

      :deep(.van-loading) {
        .van-loading__spinner {
          width: 32px;
          height: 32px;
        }

        .van-loading__text {
          font-size: 14px;
          margin-top: 12px;
        }
      }
    }

    .error-container {
      padding: 40px 16px;

      :deep(.van-empty) {
        .van-empty__image {
          width: 120px;
          height: 120px;
        }

        .van-empty__description {
          font-size: 14px;
          margin-bottom: 20px;
        }
      }

      .error-actions {
        gap: 12px;
        margin-top: 20px;

        .retry-button, .back-button {
          min-width: 100px;
          height: 40px;
          font-size: 14px;
        }
      }
    }

    .form-container {
      .status-card {
        margin: 8px;
        border-radius: 8px;

        .status-header {
          padding: 16px 20px 12px 20px;

          h3 {
            font-size: 14px;
          }

          .status-tag {
            :deep(.van-tag) {
              font-size: 12px;
              padding: 4px 8px;
            }
          }
        }

        .status-details {
          padding: 16px 20px;

          .detail-item {
            margin-bottom: 10px;

            .label {
              font-size: 13px;
            }

            .value {
              font-size: 13px;
            }
          }
        }
      }

      .order-form {
        margin: 8px;
        border-radius: 8px;
        padding: 16px;
      }
    }

    .action-bar {
      padding: 12px 16px;

      .action-buttons {
        gap: 8px;

        .van-button {
          height: 40px;
          font-size: 14px;
          border-radius: 8px;

          &.update-button {
            min-width: 80px;
          }

          &.save-button {
            min-width: 100px;
          }
        }
      }
    }
  }

  .help-popup {
    :deep(.van-popup) {
      border-radius: 12px 12px 0 0;
      max-height: 80vh;
    }

    .help-content {
      .help-header {
        padding: 16px 20px 12px 20px;

        h3 {
          font-size: 16px;
        }

        .close-icon {
          font-size: 18px;
        }
      }

      .help-body {
        padding: 20px;

        .help-item {
          margin-bottom: 16px;

          .help-icon {
            width: 36px;
            height: 36px;
            margin-right: 12px;

            .van-icon {
              font-size: 16px;
            }
          }

          .help-text {
            h4 {
              font-size: 14px;
            }

            p {
              font-size: 13px;
              line-height: 18px;
            }
          }
        }
      }

      .help-footer {
        padding: 12px 20px 20px 20px;

        .van-button {
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }
}

@media (min-width: @erp-mobile-sm) and (max-width: @erp-mobile-lg) {
  .sales-order-edit {
    // 中等屏幕优化
    .form-container {
      .status-card {
        margin: 12px;
      }

      .order-form {
        margin: 12px;
        padding: 20px;
      }
    }

    .action-bar {
      padding: 16px 20px;

      .action-buttons {
        .van-button {
          height: 44px;
          font-size: 15px;
        }
      }
    }
  }
}

@media (min-width: @erp-tablet) {
  .sales-order-edit {
    // 平板优化
    max-width: 600px;
    margin: 0 auto;

    .page-header {
      border-radius: 12px 12px 0 0;
      margin: 16px 16px 0 16px;
    }

    .progress-container {
      margin: 0 16px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .loading-container,
    .error-container {
      margin: 16px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-container {
      .status-card {
        margin: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .order-form {
        margin: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .action-bar {
      margin: 0 16px 16px 16px;
      border-radius: 0 0 12px 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .help-popup {
    :deep(.van-popup) {
      max-width: 500px;
      margin: 0 auto;
      border-radius: 16px;
    }
  }
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  .sales-order-edit {
    .page-header {
      .header-content {
        .help-icon {
          min-width: 44px;
          min-height: 44px;
        }
      }
    }

    .error-container {
      .error-actions {
        .retry-button, .back-button {
          min-height: 44px;
        }
      }
    }

    .action-bar {
      .action-buttons {
        .van-button {
          min-height: 44px;
        }
      }
    }
  }
}

// 横屏优化
@media (orientation: landscape) and (max-height: 500px) {
  .sales-order-edit {
    .progress-container {
      padding: 8px 16px;

      .progress-steps {
        .step {
          .step-number {
            width: 20px;
            height: 20px;
            font-size: 11px;
          }

          .step-label {
            font-size: 11px;
          }
        }
      }
    }

    .loading-container,
    .error-container {
      padding: 30px 16px;
    }

    .form-container {
      .status-card {
        .status-header {
          padding: 12px 20px 8px 20px;
        }

        .status-details {
          padding: 12px 20px;
        }
      }

      .order-form {
        padding: 12px;
      }
    }

    .action-bar {
      padding: 8px 16px;
    }
  }

  .help-popup {
    :deep(.van-popup) {
      max-height: 90vh;
    }
  }
}
</style>
