<!--
  新增销售订单页面

  功能：
  - 订单基本信息录入
  - 商品选择和添加
  - 金额计算
  - 保存订单
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header">
      <van-nav-bar
        title="新增单据"
        left-arrow
        @click-left="handleBack"
      >
        <template #right>
          <van-icon name="question-o" @click="showHelp" />
        </template>
      </van-nav-bar>
    </div>

    <!-- 订单表单 -->
    <div class="order-form">
      <BaseOrderForm
        ref="orderFormRef"
        :show-validation="showValidation"
        @scan-product="handleScanProduct"
        @select-product="handleSelectProduct"
      />
    </div>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <van-button
        type="success"
        size="large"
        :loading="submitting"
        :disabled="!isFormValid"
        @click="handleSaveAndSubmit"
      >
        保存并提交
      </van-button>
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        @click="handleSave"
      >
        保存草稿
      </van-button>
    </div>

    <!-- 帮助弹窗 -->
    <van-popup v-model:show="showHelpDialog" position="center" class="help-popup">
      <div class="help-content">
        <h3>操作说明</h3>
        <div class="help-item">
          <h4>1. 基础信息</h4>
          <p>请依次选择客户、订单日期和销售人员，这些为必填项</p>
        </div>
        <div class="help-item">
          <h4>2. 商品清单</h4>
          <p>可通过扫描条码或手动选择的方式添加商品</p>
        </div>
        <div class="help-item">
          <h4>3. 保存选项</h4>
          <p>• 保存草稿：保存当前内容，可稍后继续编辑</p>
          <p>• 保存并提交：完成订单并提交审核</p>
        </div>
        <van-button type="primary" block @click="showHelpDialog = false">
          我知道了
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import BaseOrderForm from '@/components/order/BaseOrderForm.vue'
import { useOrderForm } from '@/composables/useOrderForm'
import type { OrderAction } from '@/types/order'

const router = useRouter()

// 使用订单表单Hook
const {
  orderForm,
  submitting,
  isFormValid,
  validateForm,
  initializeForm
} = useOrderForm()

// 页面状态
const showValidation = ref<boolean>(false)
const showHelpDialog = ref<boolean>(false)
const orderFormRef = ref()
const hasUnsavedChanges = ref<boolean>(false)

/**
 * 返回处理
 */
const handleBack = async (): Promise<void> => {
  if (hasUnsavedChanges.value) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的内容，确定要离开吗？'
      })
    } catch {
      return // 用户取消
    }
  }
  router.back()
}

/**
 * 显示帮助
 */
const showHelp = (): void => {
  showHelpDialog.value = true
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  // TODO: 实现扫码功能
  showToast('扫码功能开发中')
  hasUnsavedChanges.value = true
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  // TODO: 实现商品选择功能
  showToast('商品选择功能开发中')
  hasUnsavedChanges.value = true
}

/**
 * 移除商品
 */
const handleRemoveProduct = (index: number): void => {
  orderForm.products.splice(index, 1)
  calculateAmount()
}

/**
 * 优惠率变化
 */
const handleDiscountChange = (): void => {
  calculateAmount()
}

/**
 * 文件超大小
 */
const handleOversizeFile = (): void => {
  showToast('文件大小不能超过1M')
}

/**
 * 保存草稿
 */
const handleSave = async (): Promise<void> => {
  showValidation.value = true

  if (!validateForm()) {
    return
  }

  submitting.value = true

  try {
    // TODO: 调用API保存订单
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用

    showToast({ type: 'success', message: '保存成功' })
    hasUnsavedChanges.value = false

    // 可选择是否返回列表页
    router.back()
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    submitting.value = false
  }
}

/**
 * 保存并提交
 */
const handleSaveAndSubmit = async (): Promise<void> => {
  showValidation.value = true

  if (!validateForm()) {
    return
  }

  try {
    await showConfirmDialog({
      title: '确认提交',
      message: '提交后将无法修改，确定要提交吗？'
    })
  } catch {
    return // 用户取消
  }

  submitting.value = true

  try {
    // TODO: 调用API保存并提交订单
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟API调用

    showToast({ type: 'success', message: '提交成功' })
    hasUnsavedChanges.value = false

    // 返回列表页
    router.back()
  } catch (error) {
    showToast({ type: 'fail', message: '提交失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 页面离开前确认
const beforeUnloadHandler = (event: BeforeUnloadEvent): void => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    event.returnValue = '当前有未保存的内容，确定要离开吗？'
  }
}

// 初始化
onMounted(() => {
  initializeForm()

  // 监听页面刷新/关闭
  window.addEventListener('beforeunload', beforeUnloadHandler)
})

// 清理
onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', beforeUnloadHandler)
})
</script>

<style lang="less" scoped>
.order-form {
  padding-bottom: 80px; // 为底部操作栏留空间
}

.product-actions {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
}

.empty-products {
  padding: 20px;
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .product-info {
    flex: 1;
    
    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .product-spec {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-quantity {
    margin: 0 12px;
  }
  
  .product-price {
    font-weight: 500;
    color: #ff6b35;
    margin-right: 12px;
  }
  
  .van-icon {
    color: #ee0a24;
    cursor: pointer;
  }
}

.amount-summary {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.attachment-info {
  padding: 12px 16px;
  
  .attachment-tip {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .attachment-note {
    font-size: 12px;
    color: #666;
    margin-top: 12px;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

  .van-button {
    flex: 1;

    &:disabled {
      opacity: 0.5;
    }
  }
}

.help-popup {
  border-radius: 12px;
  overflow: hidden;
  max-width: 320px;

  .help-content {
    padding: 24px;

    h3 {
      text-align: center;
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }

    .help-item {
      margin-bottom: 16px;

      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 8px;
      }

      p {
        font-size: 13px;
        color: #646566;
        line-height: 1.5;
        margin: 4px 0;
      }
    }

    .van-button {
      margin-top: 20px;
    }
  }
}
</style>
