<!--
  新增销售订单页面
  
  功能：
  - 订单基本信息录入
  - 商品选择和添加
  - 金额计算
  - 保存订单
-->
<template>
  <div class="erp-page">
    <!-- 页面头部 -->
    <div class="erp-header">
      <van-nav-bar
        title="新增单据"
        left-arrow
        @click-left="$router.back()"
      />
    </div>

    <!-- 订单表单 -->
    <div class="order-form">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <van-field
          v-model="orderForm.customerName"
          label="客户"
          placeholder="请选择客户"
          readonly
          @click="showCustomerPicker = true"
        />
        
        <van-field
          v-model="orderForm.orderDate"
          label="单据日期"
          readonly
          @click="showDatePicker = true"
        />
        
        <van-field
          v-model="orderForm.orderNo"
          label="单据编号"
          readonly
        />
        
        <van-field
          v-model="orderForm.salesperson"
          label="销售人员"
          placeholder="请选择销售人员"
          readonly
          @click="showSalespersonPicker = true"
        />
      </van-cell-group>

      <!-- 商品清单 -->
      <van-cell-group title="商品清单" inset>
        <div class="product-actions">
          <van-button
            type="primary"
            size="small"
            icon="scan"
            @click="handleScanProduct"
          >
            扫描条码
          </van-button>
          <van-button
            type="default"
            size="small"
            icon="plus"
            @click="handleSelectProduct"
          >
            选择商品
          </van-button>
        </div>
        
        <div v-if="orderForm.products.length === 0" class="empty-products">
          <van-empty description="暂无商品" />
        </div>
        
        <div v-else class="product-list">
          <div
            v-for="(product, index) in orderForm.products"
            :key="index"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-spec">{{ product.spec }}</div>
            </div>
            <div class="product-quantity">
              <van-stepper
                v-model="product.quantity"
                min="1"
                @change="handleQuantityChange"
              />
            </div>
            <div class="product-price">
              ¥{{ formatCurrency(product.price * product.quantity) }}
            </div>
            <van-icon
              name="delete-o"
              @click="handleRemoveProduct(index)"
            />
          </div>
        </div>
        
        <!-- 金额汇总 -->
        <div class="amount-summary">
          <van-field
            v-model="orderForm.subtotal"
            label="合税合计"
            readonly
          />
          <van-field
            v-model="orderForm.discountRate"
            label="优惠率(%)"
            type="number"
            @input="handleDiscountChange"
          />
          <van-field
            v-model="orderForm.discountAmount"
            label="收款优惠"
            readonly
          />
          <van-field
            v-model="orderForm.finalAmount"
            label="优惠后金额"
            readonly
          />
        </div>
      </van-cell-group>

      <!-- 结算信息 -->
      <van-cell-group title="结算信息" inset>
        <van-field
          v-model="orderForm.paymentAccount"
          label="结算账户"
          placeholder="测试"
          readonly
        />
        <van-field
          v-model="orderForm.receivedAmount"
          label="收取订金"
          type="number"
          placeholder="0"
        />
      </van-cell-group>

      <!-- 备注 -->
      <van-cell-group title="备注" inset>
        <van-field
          v-model="orderForm.remark"
          type="textarea"
          placeholder="请输入备注"
          rows="3"
        />
      </van-cell-group>

      <!-- 附件信息 -->
      <van-cell-group title="附件信息" inset>
        <div class="attachment-info">
          <div class="attachment-tip">
            (图片最多4张，单张大小不超过1M)
          </div>
          <van-uploader
            v-model="orderForm.attachments"
            multiple
            :max-count="4"
            :max-size="1024 * 1024"
            @oversize="handleOversizeFile"
          />
          <div class="attachment-note">
            提醒：上传word等非图片文件请到脑膜操作
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <van-button
        type="success"
        size="large"
        @click="handleSaveAndSubmit"
      >
        保存并单据
      </van-button>
      <van-button
        type="primary"
        size="large"
        @click="handleSave"
      >
        保存
      </van-button>
    </div>

    <!-- 日期选择器 -->
    <van-date-picker
      v-model:show="showDatePicker"
      v-model="dateValue"
      title="选择日期"
      @confirm="handleDateConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

/**
 * 商品接口
 */
interface Product {
  id: string
  name: string
  spec: string
  price: number
  quantity: number
}

/**
 * 订单表单接口
 */
interface OrderForm {
  customerName: string
  orderDate: string
  orderNo: string
  salesperson: string
  products: Product[]
  subtotal: number
  discountRate: number
  discountAmount: number
  finalAmount: number
  paymentAccount: string
  receivedAmount: number
  remark: string
  attachments: any[]
}

const router = useRouter()

// 响应式数据
const showCustomerPicker = ref<boolean>(false)
const showDatePicker = ref<boolean>(false)
const showSalespersonPicker = ref<boolean>(false)
const dateValue = ref<string[]>([])

// 订单表单
const orderForm = reactive<OrderForm>({
  customerName: '',
  orderDate: '',
  orderNo: '',
  salesperson: '',
  products: [],
  subtotal: 0,
  discountRate: 0,
  discountAmount: 0,
  finalAmount: 0,
  paymentAccount: '测试',
  receivedAmount: 0,
  remark: '',
  attachments: []
})

/**
 * 格式化货币
 */
const formatCurrency = (amount: number): string => {
  return amount.toFixed(2)
}

/**
 * 生成订单编号
 */
const generateOrderNo = (): string => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
  return `XSDD${year}${month}${day}${random}`
}

/**
 * 格式化日期
 */
const formatDate = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 计算金额
 */
const calculateAmount = (): void => {
  // 计算小计
  orderForm.subtotal = orderForm.products.reduce((total, product) => {
    return total + (product.price * product.quantity)
  }, 0)
  
  // 计算优惠金额
  orderForm.discountAmount = orderForm.subtotal * (orderForm.discountRate / 100)
  
  // 计算最终金额
  orderForm.finalAmount = orderForm.subtotal - orderForm.discountAmount
}

/**
 * 日期确认
 */
const handleDateConfirm = (value: string[]): void => {
  const date = new Date(value.join('-'))
  orderForm.orderDate = formatDate(date)
  showDatePicker.value = false
}

/**
 * 扫描商品
 */
const handleScanProduct = (): void => {
  // TODO: 实现扫码功能
  showToast('扫码功能开发中')
}

/**
 * 选择商品
 */
const handleSelectProduct = (): void => {
  // TODO: 实现商品选择功能
  showToast('商品选择功能开发中')
}

/**
 * 数量变化
 */
const handleQuantityChange = (): void => {
  calculateAmount()
}

/**
 * 移除商品
 */
const handleRemoveProduct = (index: number): void => {
  orderForm.products.splice(index, 1)
  calculateAmount()
}

/**
 * 优惠率变化
 */
const handleDiscountChange = (): void => {
  calculateAmount()
}

/**
 * 文件超大小
 */
const handleOversizeFile = (): void => {
  showToast('文件大小不能超过1M')
}

/**
 * 保存并提交
 */
const handleSaveAndSubmit = (): void => {
  // TODO: 实现保存并提交功能
  showToast('保存并提交功能开发中')
}

/**
 * 保存
 */
const handleSave = (): void => {
  // TODO: 实现保存功能
  showToast('保存功能开发中')
}

// 组件挂载时初始化
onMounted(() => {
  orderForm.orderNo = generateOrderNo()
  orderForm.orderDate = formatDate(new Date())
})
</script>

<style lang="less" scoped>
.order-form {
  padding-bottom: 80px; // 为底部操作栏留空间
}

.product-actions {
  display: flex;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
}

.empty-products {
  padding: 20px;
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .product-info {
    flex: 1;
    
    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
    }
    
    .product-spec {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-quantity {
    margin: 0 12px;
  }
  
  .product-price {
    font-weight: 500;
    color: #ff6b35;
    margin-right: 12px;
  }
  
  .van-icon {
    color: #ee0a24;
    cursor: pointer;
  }
}

.amount-summary {
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.attachment-info {
  padding: 12px 16px;
  
  .attachment-tip {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .attachment-note {
    font-size: 12px;
    color: #666;
    margin-top: 12px;
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  
  .van-button {
    flex: 1;
  }
}
</style>
