<!--
  采购退货添加页面
  
  基于通用业务表单组件，实现采购退货的添加功能
  包含关联入库单选择、退货原因管理、退货数量控制等
-->

<template>
  <div class="purchase-return-add">
    <!-- 页面头部 -->
    <van-nav-bar
      title="新增采购退货"
      left-text="返回"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息 -->
      <van-cell-group title="基础信息" inset>
        <!-- 退货编号 -->
        <van-field
          v-model="purchaseReturnForm.returnNo"
          label="退货编号"
          placeholder="系统自动生成"
          readonly
          :border="false"
        />
        
        <!-- 关联入库单 -->
        <van-field
          v-model="purchaseReturnForm.relatedInNo"
          label="关联入库单"
          placeholder="请选择采购入库单"
          readonly
          is-link
          required
          :border="false"
          @click="showInOrderPicker = true"
        />
        
        <!-- 供应商选择 -->
        <van-field
          v-model="purchaseReturnForm.supplierName"
          label="供应商"
          placeholder="请选择供应商"
          readonly
          is-link
          required
          :border="false"
          @click="showSupplierPicker = true"
        />
        
        <!-- 退货日期 -->
        <van-field
          v-model="purchaseReturnForm.returnDate"
          label="退货日期"
          placeholder="请选择退货日期"
          readonly
          is-link
          required
          :border="false"
          @click="showDatePicker = true"
        />
        
        <!-- 采购员 -->
        <van-field
          v-model="purchaseReturnForm.purchaser"
          label="采购员"
          placeholder="请选择采购员"
          readonly
          is-link
          required
          :border="false"
          @click="showPurchaserPicker = true"
        />
      </van-cell-group>

      <!-- 退货信息 -->
      <van-cell-group title="退货信息" inset>
        <!-- 退货原因 -->
        <van-field
          label="退货原因"
          :border="false"
        >
          <template #input>
            <van-radio-group
              v-model="purchaseReturnForm.returnReason"
              direction="horizontal"
            >
              <van-radio name="quality_issue">质量问题</van-radio>
              <van-radio name="quantity_error">数量错误</van-radio>
              <van-radio name="spec_mismatch">规格不符</van-radio>
              <van-radio name="other">其他</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        
        <!-- 退货方式 -->
        <van-field
          label="退货方式"
          :border="false"
        >
          <template #input>
            <van-radio-group
              v-model="purchaseReturnForm.returnType"
              direction="horizontal"
            >
              <van-radio name="direct_return">直接退货</van-radio>
              <van-radio name="exchange">换货</van-radio>
              <van-radio name="partial_return">部分退货</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        
        <!-- 出库仓库 -->
        <van-field
          v-model="purchaseReturnForm.warehouseName"
          label="出库仓库"
          placeholder="请选择出库仓库"
          readonly
          is-link
          required
          :border="false"
          @click="showWarehousePicker = true"
        />
        
        <!-- 退款账户 -->
        <van-field
          v-model="purchaseReturnForm.refundAccountName"
          label="退款账户"
          placeholder="请选择退款账户"
          readonly
          is-link
          required
          :border="false"
          @click="showAccountPicker = true"
        />
      </van-cell-group>

      <!-- 商品信息 -->
      <van-cell-group title="退货商品" inset>
        <!-- 商品列表 -->
        <div v-if="purchaseReturnForm.products.length > 0" class="product-list">
          <div
            v-for="(product, index) in purchaseReturnForm.products"
            :key="index"
            class="product-item"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-spec" v-if="product.spec">{{ product.spec }}</div>
              
              <!-- 数量信息 -->
              <div class="quantity-info">
                <span class="original-qty">原入库: {{ product.originalQuantity }}</span>
                <span class="max-return-qty">可退: {{ product.maxReturnQuantity }}</span>
              </div>
              
              <!-- 退货原因 -->
              <div class="return-reason">
                <van-tag
                  :type="getReturnReasonType(product.returnReason)"
                  size="small"
                >
                  {{ getReturnReasonText(product.returnReason) }}
                </van-tag>
              </div>
            </div>
            
            <div class="product-controls">
              <div class="quantity-control">
                <van-stepper
                  v-model="product.returnQuantity"
                  min="0"
                  :max="product.maxReturnQuantity"
                  @change="handleReturnQuantityChange(index, $event)"
                />
              </div>
              
              <div class="price-control">
                <van-field
                  v-model="product.returnPrice"
                  type="number"
                  placeholder="退货单价"
                  @blur="handleReturnPriceChange(index, Number($event.target.value))"
                />
              </div>
              
              <div class="amount">
                ¥{{ formatCurrency(product.returnPrice * product.returnQuantity) }}
              </div>
              
              <!-- 退货原因按钮 -->
              <van-button
                size="mini"
                type="primary"
                @click="handleReturnReason(index)"
              >
                原因
              </van-button>
              
              <van-icon
                name="delete-o"
                class="delete-btn"
                @click="removeReturnProduct(index)"
              />
            </div>
          </div>
        </div>
        
        <!-- 添加商品按钮 -->
        <van-cell
          title="添加退货商品"
          is-link
          :border="false"
          @click="handleAddProduct"
        >
          <template #icon>
            <van-icon name="plus" />
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 金额信息 -->
      <van-cell-group title="金额信息" inset>
        <!-- 退货小计 -->
        <van-field
          :model-value="formatCurrency(purchaseReturnForm.subtotal)"
          label="退货小计"
          readonly
          :border="false"
        />
        
        <!-- 优惠率 -->
        <van-field
          v-model.number="purchaseReturnForm.discountRate"
          label="优惠率(%)"
          type="number"
          placeholder="0"
          :border="false"
          @blur="handleDiscountChange"
        />
        
        <!-- 优惠金额 -->
        <van-field
          :model-value="formatCurrency(purchaseReturnForm.discountAmount)"
          label="优惠金额"
          readonly
          :border="false"
        />
        
        <!-- 最终退货金额 -->
        <van-field
          :model-value="formatCurrency(purchaseReturnForm.finalAmount)"
          label="退货金额"
          readonly
          class="final-amount"
          :border="false"
        />
      </van-cell-group>

      <!-- 其他信息 -->
      <van-cell-group title="其他信息" inset>
        <!-- 备注 -->
        <van-field
          v-model="purchaseReturnForm.remark"
          label="备注"
          type="textarea"
          placeholder="请输入退货备注"
          rows="3"
          autosize
          :border="false"
        />
      </van-cell-group>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="default"
        size="large"
        @click="handleSaveDraft"
        :loading="loading"
      >
        保存草稿
      </van-button>
      
      <van-button
        type="primary"
        size="large"
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!isFormValid"
      >
        确认退货
      </van-button>
    </div>

    <!-- 供应商选择器 -->
    <van-popup
      v-model:show="showSupplierPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <SupplierPicker @select="handleSupplierSelect" />
    </van-popup>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-date-picker
        v-model="dateValue"
        title="选择退货日期"
        @confirm="handleDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 采购员选择器 -->
    <van-popup
      v-model:show="showPurchaserPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <PurchaserPicker @select="handlePurchaserSelect" />
    </van-popup>

    <!-- 仓库选择器 -->
    <van-popup
      v-model:show="showWarehousePicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <WarehousePicker @select="handleWarehouseSelect" />
    </van-popup>

    <!-- 退款账户选择器 -->
    <van-popup
      v-model:show="showAccountPicker"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <AccountPicker @select="handleAccountSelect" />
    </van-popup>

    <!-- 采购入库单选择器 -->
    <van-popup
      v-model:show="showInOrderPicker"
      position="bottom"
      :style="{ height: '70%' }"
    >
      <PurchaseInOrderPicker @select="handleInOrderSelect" />
    </van-popup>

    <!-- 商品选择器 -->
    <van-popup
      v-model:show="showProductPicker"
      position="bottom"
      :style="{ height: '80%' }"
    >
      <ProductPicker @select="handleProductSelect" />
    </van-popup>

    <!-- 退货原因弹窗 -->
    <van-popup
      v-model:show="showReasonDialog"
      position="center"
      :style="{ width: '90%', borderRadius: '12px' }"
    >
      <div class="reason-dialog">
        <div class="dialog-header">
          <h3>退货原因</h3>
          <van-icon name="cross" @click="showReasonDialog = false" />
        </div>
        
        <div class="dialog-content">
          <div class="product-info">
            <div class="product-name">{{ currentReasonProduct?.name }}</div>
            <div class="product-spec">{{ currentReasonProduct?.spec }}</div>
          </div>
          
          <van-field
            label="退货原因"
            :border="false"
          >
            <template #input>
              <van-radio-group v-model="currentReturnReason">
                <van-radio name="quality_issue">质量问题</van-radio>
                <van-radio name="quantity_error">数量错误</van-radio>
                <van-radio name="spec_mismatch">规格不符</van-radio>
                <van-radio name="other">其他</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </div>
        
        <div class="dialog-actions">
          <van-button
            type="default"
            @click="showReasonDialog = false"
          >
            取消
          </van-button>
          
          <van-button
            type="primary"
            @click="handleReasonConfirm"
          >
            确认
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { usePurchaseReturnForm } from '@/composables/usePurchaseReturnForm'
import type { PurchaseProduct, Purchaser, ReturnReason } from '@/types/purchase'
import type { Supplier, Depot, Account } from '@/types/business'

// 组件引入
import SupplierPicker from '@/components/picker/SupplierPicker.vue'
import PurchaserPicker from '@/components/picker/PurchaserPicker.vue'
import WarehousePicker from '@/components/picker/WarehousePicker.vue'
import AccountPicker from '@/components/picker/AccountPicker.vue'
import ProductPicker from '@/components/picker/ProductPicker.vue'
import PurchaseInOrderPicker from '@/components/picker/PurchaseInOrderPicker.vue'

// 路由
const router = useRouter()

// 表单逻辑
const {
  loading,
  submitting,
  purchaseReturnForm,
  showSupplierPicker,
  showDatePicker,
  showPurchaserPicker,
  showWarehousePicker,
  showInOrderPicker,
  showAccountPicker,
  dateValue,
  isFormValid,
  totalReturnQuantity,
  formatCurrency,
  validateForm,
  handleDateConfirm,
  handleSupplierSelect,
  handlePurchaserSelect,
  handleWarehouseSelect,
  handleAccountSelect,
  handleInOrderSelect,
  addReturnProduct,
  removeReturnProduct,
  handleReturnQuantityChange,
  handleReturnPriceChange,
  handleReturnReasonChange,
  handleDiscountChange,
  resetForm,
  initializeForm
} = usePurchaseReturnForm()

// 商品选择器显示状态
const showProductPicker = ref(false)

// 退货原因相关状态
const showReasonDialog = ref(false)
const currentReasonIndex = ref(-1)
const currentReasonProduct = ref<PurchaseProduct | null>(null)
const currentReturnReason = ref<ReturnReason>('quality_issue')

/**
 * 获取退货原因类型
 */
const getReturnReasonType = (reason: string) => {
  switch (reason) {
    case 'quality_issue': return 'danger'
    case 'quantity_error': return 'warning'
    case 'spec_mismatch': return 'primary'
    default: return 'default'
  }
}

/**
 * 获取退货原因文本
 */
const getReturnReasonText = (reason: string) => {
  switch (reason) {
    case 'quality_issue': return '质量问题'
    case 'quantity_error': return '数量错误'
    case 'spec_mismatch': return '规格不符'
    default: return '其他'
  }
}

/**
 * 返回处理
 */
const handleBack = async () => {
  // 检查是否有未保存的更改
  if (purchaseReturnForm.value.products.length > 0) {
    try {
      await showConfirmDialog({
        title: '确认离开',
        message: '当前有未保存的更改，确定要离开吗？'
      })
    } catch {
      return
    }
  }
  
  router.back()
}

/**
 * 添加商品处理
 */
const handleAddProduct = () => {
  showProductPicker.value = true
}

/**
 * 商品选择处理
 */
const handleProductSelect = (product: PurchaseProduct) => {
  addReturnProduct(product)
  showProductPicker.value = false
  showToast({ type: 'success', message: '退货商品已添加' })
}

/**
 * 退货原因处理
 */
const handleReturnReason = (index: number) => {
  const product = purchaseReturnForm.value.products[index]
  currentReasonIndex.value = index
  currentReasonProduct.value = product
  currentReturnReason.value = product.returnReason || 'quality_issue'
  showReasonDialog.value = true
}

/**
 * 退货原因确认
 */
const handleReasonConfirm = () => {
  if (currentReasonIndex.value >= 0) {
    handleReturnReasonChange(currentReasonIndex.value, currentReturnReason.value)
  }
  
  showReasonDialog.value = false
  showToast({ type: 'success', message: '退货原因已保存' })
}

/**
 * 保存草稿
 */
const handleSaveDraft = async () => {
  try {
    loading.value = true
    
    // 这里调用API保存草稿
    // await savePurchaseReturnDraft(purchaseReturnForm.value)
    
    showToast({ type: 'success', message: '草稿已保存' })
  } catch (error) {
    showToast({ type: 'fail', message: '保存失败，请重试' })
  } finally {
    loading.value = false
  }
}

/**
 * 确认退货
 */
const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  try {
    submitting.value = true
    
    // 这里调用API确认退货
    // await submitPurchaseReturn(purchaseReturnForm.value)
    
    showToast({ type: 'success', message: '退货成功' })
    
    // 返回列表页
    router.replace('/purchase/return/list')
  } catch (error) {
    showToast({ type: 'fail', message: '退货失败，请重试' })
  } finally {
    submitting.value = false
  }
}

// 初始化
onMounted(() => {
  initializeForm()
})
</script>

<style lang="less" scoped>
.purchase-return-add {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .form-container {
    padding: 16px;

    .van-cell-group {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .product-list {
    .product-item {
      display: flex;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid #ebedf0;

      &:last-child {
        border-bottom: none;
      }

      .product-info {
        flex: 1;
        min-width: 0;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-spec {
          font-size: 12px;
          color: #969799;
          margin-bottom: 6px;
        }

        .quantity-info {
          display: flex;
          gap: 12px;
          margin-bottom: 6px;

          .original-qty,
          .max-return-qty {
            font-size: 11px;
            color: #646566;
            background-color: #f2f3f5;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }

        .return-reason {
          .van-tag {
            font-size: 10px;
          }
        }
      }

      .product-controls {
        display: flex;
        align-items: center;
        gap: 6px;
        flex-wrap: wrap;

        .quantity-control {
          .van-stepper {
            --van-stepper-button-width: 22px;
            --van-stepper-button-height: 22px;
            --van-stepper-input-width: 32px;
            --van-stepper-input-height: 22px;
          }
        }

        .price-control {
          width: 65px;

          .van-field {
            padding: 0;

            :deep(.van-field__control) {
              text-align: center;
              font-size: 10px;
            }
          }
        }

        .amount {
          font-size: 11px;
          font-weight: 500;
          color: #ee0a24;
          min-width: 45px;
          text-align: right;
        }

        .van-button {
          --van-button-mini-height: 22px;
          --van-button-mini-padding: 0 6px;
          --van-button-mini-font-size: 9px;
        }

        .delete-btn {
          color: #ee0a24;
          font-size: 14px;
          cursor: pointer;

          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }

  .final-amount {
    :deep(.van-field__label) {
      font-weight: 600;
      color: #323233;
    }

    :deep(.van-field__control) {
      font-weight: 600;
      color: #ee0a24;
      font-size: 16px;
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    gap: 12px;
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    z-index: 100;

    .van-button {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-weight: 500;

      &--default {
        background-color: #f7f8fa;
        border-color: #f7f8fa;
        color: #646566;
      }

      &--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        &:active {
          transform: translateY(1px);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }

        &:disabled {
          background: #c8c9cc;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }

  .reason-dialog {
    padding: 20px;

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #323233;
      }

      .van-icon {
        font-size: 18px;
        color: #969799;
        cursor: pointer;
      }
    }

    .dialog-content {
      margin-bottom: 20px;

      .product-info {
        padding: 12px;
        background-color: #f7f8fa;
        border-radius: 8px;
        margin-bottom: 16px;

        .product-name {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }

        .product-spec {
          font-size: 12px;
          color: #969799;
        }
      }

      .van-field {
        margin-bottom: 12px;
      }
    }

    .dialog-actions {
      display: flex;
      gap: 12px;

      .van-button {
        flex: 1;
        height: 40px;
        border-radius: 20px;
      }
    }
  }

  // 弹窗样式优化
  :deep(.van-popup) {
    border-radius: 16px 16px 0 0;
  }

  :deep(.van-picker) {
    background-color: #fff;
  }

  :deep(.van-picker__toolbar) {
    border-bottom: 1px solid #ebedf0;
  }

  :deep(.van-radio-group) {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .purchase-return-add {
    .product-list {
      .product-item {
        .product-controls {
          gap: 4px;

          .price-control {
            width: 55px;
          }

          .amount {
            min-width: 40px;
            font-size: 10px;
          }

          .van-button {
            --van-button-mini-padding: 0 4px;
          }
        }
      }
    }
  }
}
</style>
