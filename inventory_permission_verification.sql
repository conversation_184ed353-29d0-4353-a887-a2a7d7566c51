-- jshERP盘点业务权限验证脚本
-- 执行日期: 2025-06-26
-- 用途: 验证管理员用户盘点业务权限配置是否正确

-- 1. 检查盘点业务菜单配置
SELECT '=== 盘点业务菜单配置检查 ===' as check_type;
SELECT id, number, name, parent_number, url, enabled, delete_flag 
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
ORDER BY number;

-- 2. 检查管理员用户信息
SELECT '=== 管理员用户信息检查 ===' as check_type;
SELECT id, username, login_name, tenant_id, status 
FROM jsh_user 
WHERE login_name IN ('admin', 'waterxi');

-- 3. 检查用户角色分配
SELECT '=== 用户角色分配检查 ===' as check_type;
SELECT ub.id, ub.type, ub.key_id as user_id, ub.value as role_ids, u.login_name, u.tenant_id
FROM jsh_user_business ub
JOIN jsh_user u ON ub.key_id = CAST(u.id AS CHAR)
WHERE u.login_name IN ('admin', 'waterxi') AND ub.type = 'UserRole';

-- 4. 检查角色权限配置（重点检查）
SELECT '=== 角色权限配置检查 ===' as check_type;
SELECT id, key_id as role_id, 
       CASE 
         WHEN value LIKE '%[1107]%' THEN '✅ 盘点业务权限已配置'
         ELSE '❌ 盘点业务权限缺失'
       END as inventory_permission_status,
       CASE 
         WHEN btn_str LIKE '%"funId": 1107%' THEN '✅ 按钮权限已配置'
         ELSE '❌ 按钮权限缺失'
       END as button_permission_status
FROM jsh_user_business 
WHERE key_id IN ('4', '10') AND type = 'RoleFunctions';

-- 5. 详细权限内容检查
SELECT '=== 详细权限内容检查 ===' as check_type;
SELECT id, key_id as role_id,
       SUBSTRING(value, LOCATE('[1107]', value)-5, 30) as inventory_value_extract,
       SUBSTRING(btn_str, LOCATE('"funId": 1107', btn_str), 150) as inventory_btn_extract
FROM jsh_user_business 
WHERE key_id IN ('4', '10') AND type = 'RoleFunctions'
  AND value LIKE '%[1107]%';

-- 6. 备份表验证
SELECT '=== 备份表验证 ===' as check_type;
SELECT COUNT(*) as backup_record_count, 
       MIN(id) as min_id, 
       MAX(id) as max_id
FROM jsh_user_business_backup_20250626;

-- 7. 修复前后对比
SELECT '=== 修复前后对比 ===' as check_type;
SELECT 'BEFORE' as status, id, key_id, 
       CASE WHEN value LIKE '%[09]%' THEN 'OLD_FORMAT' ELSE 'NEW_FORMAT' END as format_check
FROM jsh_user_business_backup_20250626
UNION ALL
SELECT 'AFTER' as status, id, key_id,
       CASE WHEN value LIKE '%[1107]%' THEN 'NEW_FORMAT' ELSE 'OLD_FORMAT' END as format_check
FROM jsh_user_business 
WHERE id IN (117, 118);

-- 验证结果说明:
-- ✅ 如果所有检查都显示正确配置，则权限修复成功
-- ❌ 如果有任何检查失败，需要进一步排查
-- 
-- 预期结果:
-- 1. 盘点业务菜单配置: 3条记录 (09, 0901, 0902)
-- 2. 管理员用户: 2个用户 (admin, waterxi)  
-- 3. 用户角色分配: admin->角色4, waterxi->角色10
-- 4. 角色权限配置: 两个角色都应显示"✅ 权限已配置"
-- 5. 详细权限内容: 应包含[1107][1108][1109]和对应的funId
-- 6. 备份表: 应有2条备份记录
-- 7. 修复对比: BEFORE显示OLD_FORMAT, AFTER显示NEW_FORMAT
