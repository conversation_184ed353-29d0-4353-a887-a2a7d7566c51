/**
 * jshERP 库存盘点模块通用样式
 * 基于现代化UI设计系统
 */

// ===== 设计系统变量 =====
@primary-color: #3B82F6;
@success-color: #52c41a;
@warning-color: #fa8c16;
@error-color: #ff4d4f;
@info-color: #1890ff;

@background-light: #F7F8FA;
@background-white: #FFFFFF;
@text-primary: #333333;
@text-secondary: #888888;
@text-disabled: #CCCCCC;

@border-radius: 6px;
@border-radius-small: 4px;
@spacing-base: 16px;
@spacing-large: 24px;

@font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

// ===== 通用卡片样式 =====
.inventory-card {
  border-radius: @border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: @background-white;
  margin-bottom: @spacing-base;
  
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-weight: 600;
      color: @text-primary;
      font-family: @font-family;
    }
  }
  
  .ant-card-body {
    padding: @spacing-large;
  }
}

// ===== 搜索区域样式 =====
.inventory-search-wrapper {
  margin-bottom: @spacing-base;
  padding: @spacing-base;
  background: @background-light;
  border-radius: @border-radius;
  
  .ant-form-item {
    margin-bottom: 12px;
    
    .ant-form-item-label {
      font-weight: 500;
      color: @text-primary;
    }
  }
  
  .table-page-search-submitButtons {
    .ant-btn {
      margin-right: 8px;
      border-radius: @border-radius-small;
      font-weight: 500;
      
      &:last-child {
        margin-right: 0;
      }
      
      &.ant-btn-primary {
        background: @primary-color;
        border-color: @primary-color;
        
        &:hover {
          background: lighten(@primary-color, 10%);
          border-color: lighten(@primary-color, 10%);
        }
      }
    }
  }
}

// ===== 操作按钮区域样式 =====
.inventory-operator {
  margin-bottom: @spacing-base;
  
  .ant-btn {
    margin-right: 8px;
    border-radius: @border-radius-small;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:last-child {
      margin-right: 0;
    }
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.ant-btn-primary {
      background: @primary-color;
      border-color: @primary-color;
    }
    
    &.ant-btn-danger {
      background: @error-color;
      border-color: @error-color;
    }
  }
}

// ===== 表格样式 =====
.inventory-table {
  .ant-table {
    border-radius: @border-radius;
    overflow: hidden;
    
    .ant-table-thead > tr > th {
      background: @background-light;
      font-weight: 600;
      color: @text-primary;
      border-bottom: 2px solid #e8e8e8;
    }
    
    .ant-table-tbody > tr {
      &:hover > td {
        background: #f5f7fa;
      }
      
      > td {
        border-bottom: 1px solid #f0f0f0;
        
        .ant-btn-link {
          padding: 0;
          color: @primary-color;
          
          &:hover {
            color: lighten(@primary-color, 10%);
          }
        }
      }
    }
  }
}

// ===== 状态标签样式 =====
.inventory-status {
  &.status-draft {
    background: fade(@warning-color, 15%);
    color: @warning-color;
    border: 1px solid fade(@warning-color, 30%);
  }
  
  &.status-checking {
    background: fade(@info-color, 15%);
    color: @info-color;
    border: 1px solid fade(@info-color, 30%);
  }
  
  &.status-completed {
    background: fade(@success-color, 15%);
    color: @success-color;
    border: 1px solid fade(@success-color, 30%);
  }
}

// ===== 差异显示样式 =====
.inventory-difference {
  font-weight: 600;
  
  &.text-profit {
    color: @success-color;
    
    &::before {
      content: '+';
    }
  }
  
  &.text-loss {
    color: @error-color;
  }
  
  &.text-normal {
    color: @text-secondary;
  }
}

// ===== 表单样式 =====
.inventory-form {
  .ant-form-item {
    margin-bottom: @spacing-base;
    
    .ant-form-item-label {
      font-weight: 500;
      color: @text-primary;
      
      label {
        font-family: @font-family;
      }
    }
    
    .ant-form-item-control {
      .ant-input,
      .ant-select-selector,
      .ant-picker {
        border-radius: @border-radius-small;
        border: 1px solid #d9d9d9;
        
        &:hover {
          border-color: @primary-color;
        }
        
        &:focus,
        &.ant-picker-focused {
          border-color: @primary-color;
          box-shadow: 0 0 0 2px fade(@primary-color, 20%);
        }
      }
    }
  }
}

// ===== 弹窗样式 =====
.inventory-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-modal-title {
      font-weight: 600;
      color: @text-primary;
      font-family: @font-family;
    }
  }
  
  .ant-modal-body {
    padding: @spacing-large;
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    
    .ant-btn {
      border-radius: @border-radius-small;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: @primary-color;
        border-color: @primary-color;
      }
    }
  }
}

// ===== 上传组件样式 =====
.inventory-upload {
  .ant-upload {
    border-radius: @border-radius-small;
    border: 2px dashed #d9d9d9;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: @primary-color;
    }
  }
  
  .ant-upload-btn {
    padding: @spacing-base;
    
    .anticon {
      color: @primary-color;
      font-size: 24px;
      margin-bottom: 8px;
    }
  }
}

// ===== 预览容器样式 =====
.inventory-preview {
  border: 1px solid #d9d9d9;
  border-radius: @border-radius-small;
  padding: 12px;
  background: @background-light;
  
  .preview-summary {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8e8e8;
    font-size: 12px;
    color: @text-secondary;
    
    .summary-item {
      margin-right: @spacing-base;
      
      &.error {
        color: @error-color;
      }
      
      &.success {
        color: @success-color;
      }
    }
  }
}

// ===== 汇总信息样式 =====
.inventory-summary {
  margin-top: @spacing-base;
  padding: 12px;
  background: @background-light;
  border-radius: @border-radius-small;
  font-weight: 500;
  
  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .summary-label {
      color: @text-primary;
    }
    
    .summary-value {
      font-weight: 600;
      
      &.positive {
        color: @success-color;
      }
      
      &.negative {
        color: @error-color;
      }
    }
  }
}

// ===== 响应式设计 =====
@media (max-width: 768px) {
  .inventory-card .ant-card-body {
    padding: @spacing-base;
  }
  
  .inventory-search-wrapper {
    padding: 12px;
  }
  
  .inventory-operator {
    .ant-btn {
      margin-bottom: 8px;
      width: 100%;
    }
  }
  
  .inventory-table {
    .ant-table {
      font-size: 12px;
    }
  }
}

// ===== 动画效果 =====
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.inventory-fade-in {
  animation: fadeInUp 0.3s ease-out;
}

// ===== 加载状态样式 =====
.inventory-loading {
  .ant-spin-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
