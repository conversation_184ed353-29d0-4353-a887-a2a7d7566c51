# jshERP 库存盘点模块集成测试指南

## 🎯 测试目标

确保库存盘点模块在jshERP Docker环境中正常运行，包括菜单显示、权限控制、功能操作和数据处理等各个方面。

## 📋 测试前准备

### 环境要求
- ✅ jshERP Docker环境正常运行
- ✅ 前端服务：localhost:8080
- ✅ 后端服务：localhost:9999
- ✅ MySQL数据库：localhost:3306
- ✅ phpMyAdmin可正常访问

### 备份数据库
```bash
# 进入MySQL容器
docker exec -it jsherp-mysql bash

# 备份数据库
mysqldump -u jsh_user -p123456 jsh_erp > /tmp/jsh_erp_backup_$(date +%Y%m%d_%H%M%S).sql

# 退出容器
exit
```

## 🗄️ 第一步：数据库配置

### 1.1 访问phpMyAdmin
1. 打开浏览器访问：`http://localhost:8080/phpmyadmin`
2. 登录信息：
   - 用户名：`jsh_user`
   - 密码：`123456`
   - 数据库：`jsh_erp`

### 1.2 执行前检查
在phpMyAdmin中执行以下查询，检查现有菜单结构：

```sql
-- 查看现有一级菜单
SELECT number, name, parent_number, url, sort 
FROM jsh_function 
WHERE parent_number = '0' AND delete_flag = '0' 
ORDER BY sort;

-- 检查编号09是否已被使用
SELECT * FROM jsh_function 
WHERE number = '09' AND delete_flag = '0';

-- 查看现有角色
SELECT id, name, value, description 
FROM jsh_role 
WHERE delete_flag = '0';
```

**预期结果**：
- 如果编号09未被使用，可以直接执行配置脚本
- 如果编号09已被使用，需要修改脚本中的编号

### 1.3 执行配置脚本
1. 打开文件：`jshERP-web/src/views/inventory/database/inventory_menu_config.sql`
2. 复制菜单配置部分的SQL语句
3. 在phpMyAdmin的SQL标签页中粘贴并执行

**执行顺序**：
```sql
-- 1. 创建一级菜单：盘点业务
INSERT INTO jsh_function (...) VALUES ('09', '盘点业务', ...);

-- 2. 创建二级菜单：盘点复盘
INSERT INTO jsh_function (...) VALUES ('0901', '盘点复盘', ...);

-- 3. 创建二级菜单：盘点录入
INSERT INTO jsh_function (...) VALUES ('0902', '盘点录入', ...);
```

### 1.4 权限分配
查询当前用户的角色ID：
```sql
-- 查询当前登录用户的角色
SELECT ub.key_id, r.name as role_name, ub.value
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.delete_flag = '0';
```

根据查询结果，修改权限分配脚本中的角色ID，然后执行：
```sql
-- 为租户角色分配权限（请替换实际的角色ID）
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' 
  AND key_id = '10'  -- 替换为实际的角色ID
  AND delete_flag = '0';
```

### 1.5 验证配置
执行验证脚本：
```sql
-- 验证菜单创建
SELECT number, name, parent_number, url, component, enabled
FROM jsh_function 
WHERE number IN ('09', '0901', '0902') 
  AND delete_flag = '0'
ORDER BY sort;

-- 验证权限分配
SELECT ub.key_id, r.name as role_name, ub.value
FROM jsh_user_business ub
LEFT JOIN jsh_role r ON ub.key_id = r.id
WHERE ub.type = 'RoleFunctions' 
  AND ub.value LIKE '%[09]%'
  AND ub.delete_flag = '0';
```

**预期结果**：
- 应该看到3条菜单记录
- 权限字符串中应该包含[09][0901][0902]

## 🔄 第二步：服务重启

### 2.1 重启后端服务
```bash
# 重启jshERP后端容器
docker restart jsherp-boot

# 检查服务状态
docker ps | grep jsherp

# 查看启动日志
docker logs -f jsherp-boot
```

**预期结果**：
- 容器状态为"Up"
- 日志中无严重错误
- 服务在localhost:9999正常响应

### 2.2 重启前端服务（如果需要）
```bash
# 如果前端有缓存问题，可以重启前端容器
docker restart jsherp-web

# 检查前端服务
curl http://localhost:8080
```

### 2.3 清除浏览器缓存
1. 打开浏览器开发者工具（F12）
2. 右键点击刷新按钮
3. 选择"清空缓存并硬性重新加载"
4. 或者使用快捷键：Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)

## 🔐 第三步：登录验证

### 3.1 重新登录系统
1. 访问：`http://localhost:8080`
2. 使用管理员账户登录：
   - 用户名：`waterxi` 或 `admin`
   - 密码：系统设置的密码

### 3.2 检查菜单显示
登录后检查左侧菜单栏：
- ✅ 应该看到"盘点业务"一级菜单
- ✅ 展开后应该看到"盘点复盘"和"盘点录入"子菜单
- ✅ 菜单图标正常显示

**如果菜单不显示**：
1. 检查用户角色权限配置
2. 确认SQL脚本执行成功
3. 重新登录或清除浏览器缓存

## 🧪 第四步：功能测试

### 4.1 盘点复盘页面测试
1. 点击"盘点业务" → "盘点复盘"
2. 检查页面是否正常加载
3. 测试搜索功能
4. 测试操作按钮

**测试清单**：
- [ ] 页面正常加载，无JavaScript错误
- [ ] 搜索表单显示正常
- [ ] 操作按钮（新增、删除、刷新等）显示正常
- [ ] 表格结构正确
- [ ] 分页功能正常

### 4.2 盘点录入页面测试
1. 点击"盘点业务" → "盘点录入"
2. 检查页面是否正常加载
3. 测试表单功能

**测试清单**：
- [ ] 页面正常加载
- [ ] 单据头信息显示正常
- [ ] 明细表格可以编辑
- [ ] 添加行功能正常
- [ ] 保存按钮响应正常

### 4.3 新增盘点单测试
1. 在盘点复盘页面点击"新增"
2. 填写盘点单信息
3. 保存并验证

**测试步骤**：
```
1. 点击"新增"按钮
2. 选择盘点仓库
3. 选择盘点类型
4. 选择操作员
5. 填写备注
6. 点击"确定"保存
```

### 4.4 导入功能测试
1. 在盘点录入页面点击"导入库存盘点数据"
2. 测试模板下载
3. 测试文件上传

**测试清单**：
- [ ] 导入弹窗正常打开
- [ ] 模板下载功能正常
- [ ] 仓库选择正常
- [ ] 文件上传功能正常
- [ ] 数据预览正常

## 🐛 第五步：错误排查

### 5.1 常见问题及解决方案

#### 问题1：菜单不显示
**可能原因**：
- SQL脚本执行失败
- 权限配置错误
- 用户角色不正确

**解决方案**：
```sql
-- 检查菜单是否创建成功
SELECT * FROM jsh_function WHERE number IN ('09', '0901', '0902');

-- 检查用户权限
SELECT * FROM jsh_user_business 
WHERE type = 'RoleFunctions' AND value LIKE '%[09]%';

-- 重新分配权限
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' AND key_id = '你的角色ID';
```

#### 问题2：页面加载错误
**可能原因**：
- 组件路径错误
- 依赖模块缺失
- 路由配置问题

**解决方案**：
1. 打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的请求状态
4. 根据错误信息修复问题

#### 问题3：API调用失败
**可能原因**：
- 后端服务未启动
- API路径错误
- 权限验证失败

**解决方案**：
```bash
# 检查后端服务状态
docker ps | grep jsherp-boot

# 查看后端日志
docker logs jsherp-boot

# 测试API连通性
curl http://localhost:9999/depotHead/list
```

### 5.2 调试工具

#### 浏览器开发者工具
- **Console**：查看JavaScript错误
- **Network**：查看API请求状态
- **Application**：查看本地存储和缓存
- **Sources**：调试JavaScript代码

#### 后端日志
```bash
# 实时查看后端日志
docker logs -f jsherp-boot

# 查看最近的错误日志
docker logs jsherp-boot | grep ERROR
```

## ✅ 第六步：验收确认

### 6.1 功能验收清单
- [ ] 菜单正常显示
- [ ] 盘点复盘页面功能正常
- [ ] 盘点录入页面功能正常
- [ ] 新增盘点单功能正常
- [ ] 导入功能正常
- [ ] 权限控制正常
- [ ] 数据保存正常
- [ ] 错误处理正常

### 6.2 性能验收
- [ ] 页面加载时间 < 3秒
- [ ] 操作响应时间 < 1秒
- [ ] 大量数据处理正常
- [ ] 内存使用正常

### 6.3 兼容性验收
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] 移动端显示正常
- [ ] 不同分辨率适配正常

## 🔄 回滚方案

如果测试过程中出现严重问题，可以执行回滚：

### 数据库回滚
```sql
-- 删除创建的菜单
UPDATE jsh_function 
SET delete_flag = '1' 
WHERE number IN ('09', '0901', '0902');

-- 移除权限配置（需要手动编辑value字段）
-- 建议先备份，然后手动移除[09][0901][0902]部分
```

### 文件回滚
```bash
# 删除创建的组件文件
rm -rf jshERP-web/src/views/inventory/
```

## 📞 技术支持

如果在测试过程中遇到问题：

1. **检查错误日志**：浏览器控制台和后端日志
2. **验证配置**：数据库配置和权限设置
3. **重启服务**：清除缓存并重启相关服务
4. **回滚操作**：如果问题严重，执行回滚方案

## 📝 测试报告模板

测试完成后，请填写以下报告：

```
测试时间：____年__月__日
测试环境：Docker + jshERP
测试人员：________

功能测试结果：
□ 通过  □ 失败  菜单显示
□ 通过  □ 失败  盘点复盘页面
□ 通过  □ 失败  盘点录入页面
□ 通过  □ 失败  导入功能
□ 通过  □ 失败  权限控制

发现问题：
1. ________________
2. ________________

解决方案：
1. ________________
2. ________________

总体评价：
□ 测试通过，可以正式使用
□ 存在问题，需要修复后重新测试
□ 严重问题，建议回滚
```
