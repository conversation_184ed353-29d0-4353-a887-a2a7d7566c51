# jshERP 库存盘点模块快速测试清单

## 🚀 5分钟快速验证

### ✅ 第一步：数据库验证（1分钟）
在phpMyAdmin中执行：
```sql
-- 快速检查菜单是否创建成功
SELECT number, name, enabled FROM jsh_function 
WHERE number IN ('09', '0901', '0902') AND delete_flag = '0';

-- 快速检查权限是否分配
SELECT key_id, value FROM jsh_user_business 
WHERE type = 'RoleFunctions' AND value LIKE '%[09]%' AND delete_flag = '0';
```

**预期结果**：
- 应该看到3条菜单记录
- 至少有1条权限记录包含[09]

### ✅ 第二步：菜单显示验证（1分钟）
1. 登录jshERP系统：`http://localhost:8080`
2. 检查左侧菜单栏
3. 查找"盘点业务"菜单

**预期结果**：
- ✅ 看到"盘点业务"一级菜单
- ✅ 展开后看到"盘点复盘"和"盘点录入"

### ✅ 第三步：页面访问验证（2分钟）
1. 点击"盘点复盘"菜单
2. 点击"盘点录入"菜单
3. 检查页面是否正常加载

**预期结果**：
- ✅ 页面正常加载，无白屏
- ✅ 无JavaScript错误（F12查看控制台）
- ✅ 页面布局正常

### ✅ 第四步：基础功能验证（1分钟）
1. 在盘点复盘页面点击"新增"按钮
2. 在盘点录入页面点击"导入库存盘点数据"
3. 检查弹窗是否正常打开

**预期结果**：
- ✅ 新增弹窗正常打开
- ✅ 导入弹窗正常打开
- ✅ 表单元素显示正常

---

## 🔧 问题快速修复

### ❌ 菜单不显示
**快速检查**：
```sql
-- 检查菜单是否存在
SELECT * FROM jsh_function WHERE number = '09';

-- 检查权限是否分配
SELECT * FROM jsh_user_business WHERE value LIKE '%[09]%';
```

**快速修复**：
```sql
-- 重新分配权限（替换YOUR_ROLE_ID）
UPDATE jsh_user_business 
SET value = CONCAT(IFNULL(value, ''), '[09][0901][0902]')
WHERE type = 'RoleFunctions' AND key_id = 'YOUR_ROLE_ID';
```

### ❌ 页面加载错误
**快速检查**：
1. 按F12打开开发者工具
2. 查看Console标签页的错误信息
3. 查看Network标签页的请求状态

**常见错误及修复**：
- **404错误**：组件路径错误，检查component字段
- **权限错误**：用户权限不足，重新分配权限
- **依赖错误**：缺少依赖模块，检查import语句

### ❌ 弹窗无法打开
**快速检查**：
```javascript
// 在浏览器控制台执行
console.log('Vue组件:', this.$refs);
console.log('Modal组件:', this.$refs.modalForm);
```

**快速修复**：
1. 刷新页面（Ctrl+F5）
2. 清除浏览器缓存
3. 重启Docker容器

---

## 📋 详细测试清单

### 🗄️ 数据库配置测试
- [ ] 菜单记录创建成功
- [ ] 权限记录分配成功
- [ ] 菜单层级关系正确
- [ ] 组件路径配置正确
- [ ] 按钮权限配置正确

### 🖥️ 前端显示测试
- [ ] 一级菜单"盘点业务"显示
- [ ] 二级菜单"盘点复盘"显示
- [ ] 二级菜单"盘点录入"显示
- [ ] 菜单图标正常显示
- [ ] 菜单点击响应正常

### 📄 页面功能测试

#### 盘点复盘页面
- [ ] 页面正常加载
- [ ] 搜索表单显示正常
- [ ] 操作按钮显示正常
- [ ] 表格结构正确
- [ ] 新增按钮可点击
- [ ] 新增弹窗正常打开

#### 盘点录入页面
- [ ] 页面正常加载
- [ ] 单据头信息显示
- [ ] 明细表格显示
- [ ] 导入按钮可点击
- [ ] 导入弹窗正常打开
- [ ] 添加行功能正常

#### 新增盘点单弹窗
- [ ] 弹窗正常打开
- [ ] 表单字段显示完整
- [ ] 仓库下拉选择正常
- [ ] 盘点类型选择正常
- [ ] 操作员选择正常
- [ ] 保存按钮响应正常

#### 导入功能弹窗
- [ ] 弹窗正常打开
- [ ] 模板下载链接正常
- [ ] 仓库选择下拉正常
- [ ] 文件上传组件正常
- [ ] 导入选项显示正常

### 🔐 权限控制测试
- [ ] 管理员用户可访问所有功能
- [ ] 普通用户权限控制正常
- [ ] 按钮权限控制生效
- [ ] 数据权限隔离正常

### 📱 响应式测试
- [ ] 桌面端显示正常
- [ ] 平板端显示正常
- [ ] 手机端显示正常
- [ ] 不同分辨率适配正常

### ⚡ 性能测试
- [ ] 页面加载时间 < 3秒
- [ ] 菜单切换响应 < 1秒
- [ ] 弹窗打开响应 < 1秒
- [ ] 表格渲染正常

---

## 🐛 常见问题速查

| 问题现象 | 可能原因 | 快速解决 |
|---------|---------|---------|
| 菜单不显示 | 权限未分配 | 重新执行权限分配SQL |
| 页面404错误 | 组件路径错误 | 检查component字段配置 |
| 弹窗无法打开 | 组件引用错误 | 检查import和components配置 |
| 样式显示异常 | CSS文件未加载 | 检查样式文件路径 |
| API调用失败 | 后端服务异常 | 重启Docker容器 |
| 权限验证失败 | 用户角色配置错误 | 检查用户角色分配 |

---

## 📞 紧急联系

如果快速测试失败，请按以下顺序排查：

1. **检查Docker服务状态**
   ```bash
   docker ps | grep jsherp
   docker logs jsherp-boot
   ```

2. **检查数据库连接**
   ```bash
   docker exec -it jsherp-mysql mysql -u jsh_user -p123456 jsh_erp
   ```

3. **检查前端服务**
   ```bash
   curl http://localhost:8080
   ```

4. **查看浏览器错误**
   - 按F12打开开发者工具
   - 查看Console和Network标签页

5. **重启所有服务**
   ```bash
   docker restart jsherp-boot jsherp-web jsherp-mysql
   ```

---

## ✅ 测试通过标准

当以下所有项目都通过时，表示集成测试成功：

### 基础功能 ✅
- [x] 菜单正常显示
- [x] 页面正常加载
- [x] 弹窗正常打开
- [x] 基础操作响应正常

### 核心功能 ✅
- [x] 新增盘点单功能正常
- [x] 导入功能正常
- [x] 数据保存正常
- [x] 权限控制正常

### 用户体验 ✅
- [x] 界面美观，符合设计规范
- [x] 操作流畅，响应及时
- [x] 错误提示清晰
- [x] 移动端适配良好

### 系统稳定性 ✅
- [x] 无JavaScript错误
- [x] 无API调用失败
- [x] 内存使用正常
- [x] 长时间运行稳定

**🎉 恭喜！当所有测试项目都通过时，jshERP库存盘点模块已成功集成！**
